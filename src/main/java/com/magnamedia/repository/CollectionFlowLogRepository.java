package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.module.type.ContractStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.data.repository.query.Param;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 3/5/2022
 **/

@Repository
public interface CollectionFlowLogRepository extends BaseRepository<CollectionFlowLog> {

    List<CollectionFlowLog> findByEndedFalseOrderByIdDesc();

    List<CollectionFlowLog> findByEndedTrueAndRejectionTodoIsRescheduledWhenEndedTrue();

    @Query("select count(c) from CollectionFlowLog c where c.contract = ?1 and c.ended = false")
    Integer countCollectionFlowLogsByContract(Contract contract);

    @Query("select count(c) from CollectionFlowLog c inner join c.contract co where co.client = ?1 and c.ended = false")
    Integer countCollectionFlowLogsByClient(Client client);

    Page<CollectionFlowLog> findByContractAndEndedFalse(Contract contract, Pageable pageable);

    CollectionFlowLog findTopByFlowTypeAndRelatedToIdAndRelatedToEntityAndContractAndEndedFalse(PicklistItem flowType, Long relatedToId, String relatedToEntity, Contract contract);

    boolean existsByFlowTypeAndContractAndEndedFalse(PicklistItem flowType, Contract contract);

    @Query("select c from Contract c " +
            "where c.isOneMonthAgreement = true and c.status in ?1 " +
            "and not exists (select 1 from CollectionFlowLog l where l.contract = c " +
                "and l.flowType.code ='one_month_agreement_flow' and l.ended = false)")
    List<Contract> findContractFlaggedOneMonthAgreementWithoutLogs(List<ContractStatus> statuses);

    @Query("select distinct c.flowType.name " +
            "from CollectionFlowLog c " +
            "where c.contract = ?1 and c.ended = false")
    List<String> findByContractAndEndedFalse(Contract contract);

    @Query("select c from Contract c " +
            "where c.payingViaCreditCard = true and c.isOneMonthAgreement = false and c.status in ?1 " +
            "and not exists (select 1 from CollectionFlowLog l where l.contract = c " +
                "and l.flowType.code in ('client_paying_via_credit_card_flow', 'extension_flow') and l.ended = false) and " +
            "not exists (select 1 from FlowProcessorEntity f where f.contractPaymentTerm.contract = c and " +
                "f.stopped = false and f.completed = false and f.flowEventConfig.name = 'EXTENSION_FLOW')")
    List<Contract> findContractFlaggedPayingViaCreditCardWithoutLogs(List<ContractStatus> statuses);

    @Query("select f from FlowProcessorEntity f " +
            "join f.contractPaymentTerm.contract c " +
            "where c.status = 'ACTIVE' and c.scheduledDateOfTermination is null and " +
            "f.flowEventConfig.name = 'PAYMENT_EXPIRY_FLOW' and f.stopped = false and f.completed = false and " +
                "not exists (select 1 from CollectionFlowLog l where l.contract = c and " +
                    "l.flowType.code ='payment_expiry_flow' and l.ended = false)")
    List<FlowProcessorEntity> findActivePaymentExpiryFlowsWithoutLogs();

    @Query("select c from Contract c " +
            "where c.status not in ('CANCELLED', 'EXPIRED') and " +
                "exists (select 1 from FlowProcessorEntity f where f.contractPaymentTerm.contract = c and " +
                    "f.stopped = false and f.completed = false and f.flowEventConfig.name = ?2) and " +
                "not exists (select 1 from CollectionFlowLog l where l.contract = c and " +
                    "l.flowType.code = ?1 and l.ended = false) ")
    List<Contract> findFlowProcessorEntityWithoutLogs(String logType, FlowEventConfig.FlowEventName flowEventName);

    @Query("select f from FlowProcessorEntity f " +
            "join f.contractPaymentTerm.contract c " +
            "where c.status not in ('CANCELLED', 'EXPIRED') and " +
                "f.stopped = false and f.completed = false and f.flowEventConfig.name = ?2 and " +
                "not exists (select 1 from CollectionFlowLog l where l.contract = c and l.relatedToId = f.id and " +
                    "l.relatedToEntity = 'FlowProcessorEntity' and l.flowType.code = ?1 and l.ended = false) ")
    List<FlowProcessorEntity> findAllFlowProcessorEntityWithoutLogs(String logType, FlowEventConfig.FlowEventName flowEventName);

    @Query("select distinct ft.code as flowCode "+
            "from CollectionFlowLog l " +
            "inner join l.flowType ft " +
            "where l.contract.id = :contractId and l.ended = false")
    List<Object[]> getFlowStatusByContractId(@Param("contractId") Long contractId);

    @Query("select distinct ft.code as flowCode "+
            "from CollectionFlowLog l " +
            "inner join l.flowType ft " +
            "where l.contract.id = :contractId and l.ended = false and ft.code in :flowCodes")
    List<Object[]> getFlowStatusByContractIdWithFilter(@Param("contractId") Long contractId, @Param("flowCodes") List<String> flowCodes);
}
