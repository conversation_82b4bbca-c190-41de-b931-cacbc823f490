package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface PaymentRepository extends BaseRepository<Payment> {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Query("select p from Payment p where p.id = ?1")
    Payment findOldPayment(Long id);

    List<Payment> findByContractAndIsProRatedAndStatusIn(Contract contract, boolean isProRated, List<PaymentStatus> statuses);

    List<Payment> findByContractAndIsInitialAndStatusIn(Contract contract, boolean isInitial, List<PaymentStatus> statuses);

    Page<Payment> findByDateOfPaymentBetweenAndStatus(
            Date dateofPayment1, Date dateofPayment2, PaymentStatus status, Pageable pageable);

    Page<Payment> findByDateOfPaymentBetweenAndStatusAndIdNotIn(
            Date dateofPayment1, Date dateofPayment2,
            PaymentStatus status, List<Long> payments, Pageable pageable);

    List<Payment> findByContractAndStatusIn(Contract contract, List<PaymentStatus> statuses);

    List<Payment> findByContractAndStatusOrderByDateOfPaymentDesc(Contract contract, PaymentStatus status);

    List<Payment> findByContractAndTypeOfPayment(Contract contract, PicklistItem typeOfPayment);

    List<Payment> findByContractOrderByDateOfPaymentDesc(Contract contract);

    @Query(nativeQuery = true,value = "select   p.DATE_OF_PAYMENT from PAYMENTS p " +
            "where p.CONTRACT_ID = ?1 and p.TYPE_OF_PAYMENT_ID = ?2 and p.status= ?3 " +
            "order by p.DATE_OF_PAYMENT desc limit 1")
    Date findFirstDateOfPaymentByContractAndTypeOfPaymentAndStatusOrderByDateOfPaymentDesc(long contractID,long typeOfPaymentID, String status);

    List<Payment> findByStatusAndContractAndDateOfPaymentAndAmountOfPaymentAndTypeOfPaymentAndReplaced(
            PaymentStatus status, Contract contract, Date d, Double amountOfPayment, PicklistItem type, Boolean replaced);

    @Query("select p from ContractPaymentWrapper w " +
            "inner join Payment p on p.id = w.replacedBouncedPaymentId " +
            "where w.generatedPaymentId = ?1 and p.status = 'BOUNCED' and p.replaced = false")
    List<Payment> findByReplacedBouncedPaymentId(Long id);

    List<Payment> findByStatusAndContractAndDateOfPaymentAndAmountOfPaymentAndTypeOfPayment(
            PaymentStatus status, Contract contract, Date d, Double amountOfPayment, PicklistItem type);
    Boolean existsByStatusAndContractAndDateOfPaymentAndAmountOfPaymentAndTypeOfPayment(
            PaymentStatus status, Contract contract, Date d, Double amountOfPayment, PicklistItem type);

    Boolean existsByStatusAndContractAndDateOfPaymentAndTypeOfPayment(
            PaymentStatus status, Contract contract, Date d, PicklistItem type);

    List<Payment> findByContractAndTypeOfPayment_CodeAndStatus(
            Contract contract, String typeOfPayment, PaymentStatus status);

    //ACC-4942
    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.methodOfPayment <> 'DIRECT_DEBIT' and p.typeOfPayment.code = 'monthly_payment' " +
                "and p.status = 'RECEIVED' and YEAR(p.dateOfPayment) = ?2 and MONTH(p.dateOfPayment) = ?3 " +
                "and (p.contract.contractProspectType.code = 'maidvisa.ae_prospect' " +
                    "or exists(select 1 from Payment p1 where p1.contract = ?1 and p1.isProRated = true))")
    Boolean validatePaymentReceivedWithinStartOfContractMonth(Contract contract, int year, int month);

    // ACC-333
    // ACC-405
    List<Payment> findByContract_IdAndDateOfPaymentAndAmountOfPayment(Long contract_id, Date startDate, Double amountOfPayment);

    Payment findFirstByContract_IdAndStatusInOrderByDateOfPaymentAsc(Long contract_id, List<PaymentStatus> status);

    Payment findFirstByClientRefundToDo_IdOrderByDateOfPaymentAsc(Long refundID);

    @Query("select sum(p.amountOfPayment) from Payment p where p.id in :ids")
    Double sumByPaymentIdsList(@Param("ids") List<Long> idsList);

    // ACC-1092
    @Query("select p from Payment p " +
            "where p.contract = ?1 and p.dateOfPayment >= ?2 and p.dateOfPayment <= ?3 " +
                "and p.typeOfPayment.code = 'monthly_payment' " +
                "and p.status in ('PDC', 'PRE_PDP', 'RECEIVED', 'BOUNCED')")
    List<Payment> findMonthlyPaymentByContractAndDateOfPaymentBetween(
            Contract contract, Date dateOfPayment, Date dateOfPayment2);

    // ACC-1134
    Long countByContractAndStatusAndTypeOfPaymentAndMethodOfPayment(
            Contract contract, PaymentStatus status, PicklistItem typeOfPayment, PaymentMethod methodOfPayment);

    int countByContractAndStatusAndTypeOfPayment_Code(Contract contract, PaymentStatus status, String code);

    @Query(" SElECT p FROM Payment p " +
            "WHERE p.contract = :contract AND p.status in :statusList AND " +
                "p.typeOfPayment.code in :typeOfPaymentCodeList AND " +
                "(null = :date OR p.dateOfPayment >= :date) " +
            "ORDER BY p.dateOfPayment ASC")
    List<Payment> getContractRemainingPayment(
            @Param("contract") Contract contract, @Param("statusList") List<PaymentStatus> statusList,
            @Param("typeOfPaymentCodeList") List<String> typeOfPaymentCodeList, @Param("date") Date date);

    boolean existsByMethodOfPaymentAndOnlineAndContract_ClientAndStatusNotIn(
            PaymentMethod methodOfPayment, boolean online, Client client, List<PaymentStatus> statuses);

    boolean existsByDirectDebitIdAndStatusNotInAndReplacedAndIdNotIn(Long ddId, List<PaymentStatus> statuses, boolean replaced, List<Long> ids);

    List<Payment> findByDirectDebitIdAndStatus(Long directDebitId, PaymentStatus status);

    List<Payment> findByDirectDebitIdAndStatusAndReplaced(Long directDebitId, PaymentStatus status, boolean replaced);

    List<Payment> findByDirectDebitIdAndStatusIn(Long directDebitId, List<PaymentStatus> statuses);

    List<Payment> findByDirectDebitId(Long directDebitId);

    // ACC-1633
    Payment findFirstByContractAndStatusAndTypeOfPaymentAndDateOfPaymentGreaterThanEqualAndDateOfPaymentLessThanEqual(
            Contract contract, PaymentStatus status, PicklistItem typeOfPayment,
            Date startDate, Date endDate);

    List<Payment> findByContractAndRequiredForUnfitToWork(Contract contract, Boolean requiredForUnfitToWork);

    List<Payment> findByContractAndRequiredForUnfitToWorkAndIdNotIn(Contract contract, Boolean requiredForUnfitToWork, List<Long> ids);

    @Query("SELECT count(p.id) > 0 FROM Payment p " +
            "WHERE p.contract = ?1 AND p.id NOT IN ?2 " +
                "AND (p.requiredForUnfitToWork = 1 OR p.requiredForBouncing = 1)")
    Boolean existsByContractAndRequiredAndIdNotIn(Contract contract, List<Long> ids);

    List<Payment> findByContractAndRequiredForBouncing(Contract contract, Boolean requiredForBouncing);

    List<Payment> findByContractAndRequiredForBouncingAndIdNotIn(Contract contract, Boolean requiredForBouncing, List<Long> ids);

    Payment findFirstByContractAndStatusAndTypeOfPaymentAndIsProRatedOrderByDateOfPaymentDesc(Contract contract, PaymentStatus status, PicklistItem type, Boolean isProrated);

    @Query(value = " SELECT  p FROM Payment p WHERE p.dateOfPayment = :reportDate AND p.id NOT IN " +
            "(SELECT t.payment.id FROM BankStatementTransaction t \n" +
            "INNER JOIN DirectDebitFile ddf ON ddf.id = t.directDebitFile.id  " +
            "WHERE t.file.id = :fileId AND " +
            "t.payment.id IS NOT NULL AND " +
            "t.directDebitFile.id IS NOT NULL)")
    Page<Payment> findByFileAndMatchedWithDirectDebit(@Param("fileId") Long fileId, @Param("reportDate") java.sql.Date reportDate, Pageable pageable);


    @Query(value = "SELECT  SUM(p.amountOfPayment) FROM Payment p WHERE p.dateOfPayment = :reportDate AND p.id NOT IN " +
            "(SELECT t.payment.id FROM BankStatementTransaction t \n" +
            "INNER JOIN DirectDebitFile ddf ON ddf.id = t.directDebitFile.id  " +
            "WHERE t.file.id = :fileId AND " +
            "t.payment.id IS NOT NULL AND " +
            "t.directDebitFile.id IS NOT NULL)")
    Double sumAmountOfPaymentByFileAndMatchedWithDirectDebit(@Param("fileId") Long fileId, @Param("reportDate") java.sql.Date reportDate);

    List<Payment> findByBouncedFlowPausedForReplacement(Boolean bouncedFlowPausedForReplacement);

    // ACC-2651
    Payment findFirstByContractAndTypeOfPayment_CodeAndStatusAndDateOfPaymentBetween(
            Contract contract, String typeOfPayment, PaymentStatus status, Date startDate, Date endDate);

    Payment findFirstByContractAndAmountOfPaymentAndMethodOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetween(
            Contract contract, Double amountOfPayment, PaymentMethod methodOfPayment, String type, Date startDate, Date endDate);

    Payment findFirstByContractAndAmountOfPaymentAndMethodOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetweenAndStatusNot(
            Contract contract, Double amountOfPayment, PaymentMethod methodOfPayment, String type, Date startDate, Date endDate, PaymentStatus s);

    // ACC-2651
    Payment findFirstByContractAndStatusAndAmountOfPaymentAndMethodOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetween(
            Contract contract, PaymentStatus s, Double amountOfPayment, PaymentMethod methodOfPayment,
            String type, Date startDate, Date endDate);

    @Query("select p from Payment p " +
           "where p.contract = ?1 and p.status <> 'DELETED' and " +
           "p.amountOfPayment = ?2 and p.methodOfPayment = ?3 and " +
           "p.typeOfPayment.code = ?4 and p.dateOfPayment = ?5 " +
           "order by case p.status " +
                "when 'PDC' then 1 " +
                "when 'PRE_PDP' then 2 " +
                "else 3 end")
    List<Payment> findPaymentByContractAndCriteriaOrderedByStatus(
            Contract contract, Double amountOfPayment, PaymentMethod methodOfPayment, String type, Date date);

    // ACC-3597
    List<Payment> findByContractAndMethodOfPaymentAndStatusAndTypeOfPaymentAndReplaced(
            Contract contract, PaymentMethod methodOfPayment, PaymentStatus status, PicklistItem type, boolean replaced);

    //ACC-3968
    Payment findByReplacementForAndStatus (Payment replacementFor, PaymentStatus status);

    @Query("select p from Payment p inner join DirectDebit d on p.directDebitId = d.id where d.contractPaymentTerm.id = ?1 " +
            "and p.dateOfPayment  = ?2 and p.typeOfPayment.code = 'monthly_payment' order by p.creationDate desc")
    List<Payment> findByDirectDebit_ContractPaymentTerm_IdAndDateOfPaymentOrderByCreationDateDesc(Long cptId, Date dateOfPayment);

    //ACC-3856
    List<Payment> findByMethodOfPaymentAndStatus(PaymentMethod methodOfPayment, PaymentStatus status);

    @Query(nativeQuery = true, value =
            "SELECT p.DATE_OF_PAYMENT as dateOfPayment, p.AMOUNT_OF_PAYMENT as amount, " +
                "t.NAME as typeOfPayment, t.CODE as typeCode, " +
                "cp.CONTRACT_PAYMENT_TERM_ID as cptId, a._UUID as taxInvoice, p.DATE_CHANGED_TO_RECEIVED as taxInvoiceDate " +
            "FROM PAYMENTS p " +
            "INNER JOIN PICKLISTS_ITEMS t ON p.TYPE_OF_PAYMENT_ID = t.ID " +
            "LEFT JOIN CONTRACTPAYMENTS cp ON cp.CONTRACT_PAYMENT_TERM_ID IN " +
                "(SELECT ID FROM CONTRACTPAYMENTTERMS WHERE CONTRACT_ID = p.CONTRACT_ID) " +
                "AND p.AMOUNT_OF_PAYMENT = cp.AMOUNT AND p.TYPE_OF_PAYMENT_ID = cp.PAYMENT_TYPE_ID " +
                "AND MONTH(p.DATE_OF_PAYMENT) = MONTH(cp.DATE) AND YEAR(p.DATE_OF_PAYMENT) = YEAR(cp.DATE) " +
            "LEFT JOIN ATTACHMENTS a ON a.OWNER_TYPE = 'Payment' AND a.OWNER_ID = p.ID AND a.TAG LIKE 'payment_tax_invoice%' " +
            "WHERE p.CONTRACT_ID = ?1 AND p.STATUS = 'RECEIVED' AND p.AMOUNT_OF_PAYMENT > 0 AND " + // ACC-7050 ACC-7279
                "NOT EXISTS (SELECT ID FROM PICKLIST_ITEM_TAGS pit INNER JOIN TAGS t2 ON t2.ID = pit.TAG " +
                            "WHERE pit.ITEM = t.ID AND t2.NAME = 'refund') " +
            "ORDER BY p.DATE_OF_PAYMENT DESC")
    List<Object[]> findByContractAndStatusCCAppPaymentsHistory(Long contractId);

    @Query(nativeQuery = true, value =
            "SELECT  a.ID as id, a.VERSION as version, a.CREATION_DATE as creationDate, " +
                    "a.CREATOR as creator, a._UUID as uuid, " +
                    "a.ENTITY_TYPE as entityType, a.ATTACHMENTS_COUNT as attachmentsCount, " +
                    "a.NAME as name, a.SIZE as size, a.EXTENSION as extension, a.PATH as path, " +
                    "a.TAG as tag, a.OWNER_TYPE as ownerType, a.OWNER_ID as ownerId, " +
                    "a.UNIQUE_TAG as uniqueTag, a.AMAZON as amazon, a.KEEP_ORIGINAL_SIZE as keepOriginalSize " +
            "FROM PAYMENTS p " +
            "INNER JOIN PICKLISTS_ITEMS t ON p.TYPE_OF_PAYMENT_ID = t.ID " +
            "LEFT JOIN CONTRACTPAYMENTS cp ON cp.CONTRACT_PAYMENT_TERM_ID IN " +
                    "(SELECT ID FROM CONTRACTPAYMENTTERMS WHERE CONTRACT_ID = p.CONTRACT_ID) " +
                    "AND p.AMOUNT_OF_PAYMENT = cp.AMOUNT AND p.TYPE_OF_PAYMENT_ID = cp.PAYMENT_TYPE_ID " +
                    "AND MONTH(p.DATE_OF_PAYMENT) = MONTH(cp.DATE) AND YEAR(p.DATE_OF_PAYMENT) = YEAR(cp.DATE) " +
            "INNER JOIN ATTACHMENTS a ON a.OWNER_TYPE = 'Payment' AND a.OWNER_ID = p.ID AND a.TAG LIKE 'payment_tax_invoice%' " +
            "WHERE p.CONTRACT_ID = ?1 AND p.STATUS = 'RECEIVED' AND p.AMOUNT_OF_PAYMENT > 0 AND " + // ACC-7050 ACC-7279
                "NOT EXISTS (SELECT ID FROM PICKLIST_ITEM_TAGS pit INNER JOIN TAGS t2 ON t2.ID = pit.TAG " +
                    "WHERE pit.ITEM = t.ID AND t2.NAME = 'refund') " +
            "ORDER BY p.DATE_OF_PAYMENT DESC " +
            "LIMIT ?2")
    List<Object[]> getAllTaxInvoices(Long contractId, Integer limit);

    @Query("Select new map(p.dateOfPayment as dateOfPayment, p.amountOfPayment as amount) " +
            "from Payment p where p.contract = ?1 and p.status IN ('PRE_PDP', 'PDC') and typeOfPayment.code = 'monthly_payment' " +
            "order by p.dateOfPayment asc ")
    List<?> findUpcomingMonthlyPaymentByContract(Contract contract, Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT p.DATE_OF_PAYMENT, SUM(p.AMOUNT_OF_PAYMENT) " +
            "FROM PAYMENTS p " +
            "INNER JOIN PICKLISTS_ITEMS i ON p.TYPE_OF_PAYMENT_ID = i.ID " +
            "WHERE p.CONTRACT_ID = ?1 AND p.STATUS = 'PDC' AND " +
                "NOT EXISTS( SELECT * FROM PICKLIST_ITEM_TAGS it INNER JOIN TAGS t ON it.TAG = t.ID WHERE it.ITEM = i.ID AND t.NAME = 'refund') " +
            "GROUP BY p.DATE_OF_PAYMENT " +
            "ORDER BY p.DATE_OF_PAYMENT ASC " +
            "LIMIT 1")
    List<Object[]> findUpcomingPaymentsByContract(Long contractId);

    @Query("Select p from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = 'monthly_payment' and " +
            "p.status in ('RECEIVED', 'BOUNCED')")
    List<Payment> findActiveMonthlyPaymentByContract(Contract contract);

    @Query("select p from Payment p inner join p.contract c where p.status = ?1 and p.replaced = false and c.isScheduledForTermination = false")
    List<Payment> findByStatus(PaymentStatus status);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.recurring = true and p.status <> 'DELETED' and " +
            "p.typeOfPayment.code = 'monthly_payment' and p.dateOfPayment = ?2")
    boolean existByPaymentRecurringStatusAndDate(Contract contract, Date date);

    Boolean existsByContractAndStatusAndTypeOfPayment_CodeAndMethodOfPaymentNotInAndDateOfPaymentBetween(
            Contract contract, PaymentStatus status, String typeOfPayment, List<PaymentMethod> paymentMethod, Date startDate, Date endDate);

    Boolean existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
            Contract contract, PaymentStatus status, String typeOfPayment, Date startDate, Date endDate);

    Payment findFirstByContractAndStatusAndReplacedFalseOrderByDateOfPaymentAsc(Contract contract, PaymentStatus status);

    Payment findFirstByContractAndStatusAndTypeOfPayment_CodeOrderByDateOfPaymentDesc(
        Contract contract, PaymentStatus status, String code);

    @Query("select p from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = ?2 and p.status <> 'DELETED' and " +
                "(?3 is null or p.status = ?3) and p.replaced = false and " +
                "(?4 is null or p.dateOfPayment >= ?4) and " +
                "(?5 is null or p.dateOfPayment <= ?5) and " +
                "(p.methodOfPayment <> 'DIRECT_DEBIT' or p.status = 'RECEIVED' or " +
                    "not exists(select 1 from DirectDebitCancelationToDo toDo " +
                                "where toDo.directDebitFile.id = p.directDebitFileId " +
                                    "and toDo.stopped = false and toDo.completed = false)) " +
            "order by p.dateOfPayment desc, " +
                "(case when p.status = 'RECEIVED' then 0 when p.status = 'BOUNCED' then 1 else 2 end) asc, " +
                "p.creationDate desc")
    List<Payment> findByContractAndTypeOfPaymentAndDateOfPaymentDesc(
            Contract contract, String typeOfPayment, PaymentStatus paymentStatus, Date startDate, Date endDate);

    @Query("select new map(p.dateOfPayment as date, p.id as id) " +
            "from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code in ?3 and " +
            "p.status = 'RECEIVED' and (?2 is null or p.includeWorkerSalary = ?2 or p.typeOfPayment.code <> 'monthly_payment') " +
            "order by " +
                "case when ?4 = true then p.dateOfPayment end desc, " +
                "case when ?4 = false then p.dateOfPayment end asc")
    List<Map<String, Object>> findPaymentsByContractAndTypeAndStatus(
            Contract contract, Boolean checkWorkerSalary, List<String> types, Boolean orderDesc);

    @Query("select p from Payment p " +
            "where p.contract = ?1 and p.status <> 'DELETED' " +
                "and p.typeOfPayment.code NOT IN ('monthly_payment', 'insurance', 'same_day_recruitment_fee') " +
            "order by p.dateOfPayment asc, p.creationDate desc")
    List<Payment> findNonMonthlyPaymentsForContractSummary(Contract contract);

    @Query("select new map(p.id as id, p.amountOfPayment as amount, p.dateOfPayment as date) " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' " +
            "and p.typeOfPayment.code = 'same_day_recruitment_fee' and p.dateOfPayment < ?2 " +
            "order by p.dateOfPayment desc")
    List<Map<String, Object>> findSdrRenewalReceivedPayments(Contract contract, Date endDate);

    @Query("SELECT p FROM Payment p " +
            "WHERE p.contract = ?1 AND p.dateOfPayment = ?2 AND p.amountOfPayment = ?3 AND " +
                "p.typeOfPayment.code = 'monthly_payment' AND p.methodOfPayment = 'CARD' AND p.online = true")
    Payment findOnlineCardMonthlyPayment(Contract contract, Date date, Double amount);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.methodOfPayment <> 'DIRECT_DEBIT' and p.status = 'RECEIVED' " +
                "and YEAR(p.dateChangedToReceived) = ?2 and MONTH(p.dateChangedToReceived) = ?3")
    Boolean paymentReceivedWithinDateAndMethodOfPaymentNotDirectDebit(Contract contract, int year, int month);

    @Query("select count (p.id) from Payment p " +
            "where p.typeOfPayment.code = 'monthly_payment' and p.status = 'RECEIVED' " +
                "and p.methodOfPayment <> 'DIRECT_DEBIT' and p.contract = ?1")
    int findCountByMonthlyPaymentReceivedAndCreditCardAndContract(Contract contract);

    Boolean existsByContractAndAmountOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetweenAndStatus(
            Contract contract, Double amountOfPayment,
            String type, Date startDate, Date endDate, PaymentStatus status);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' and p.typeOfPayment = ?2 " +
                "and p.dateOfPayment >= ?3 and p.dateOfPayment < ?4")
    boolean paymentReceived(Contract contract, PicklistItem type, Date fromDate, Date toDate);

    @Query(nativeQuery = true,
            value = "select p.ID " +
                    "from PAYMENTS p " +
                    "inner join PAYMENTS_REVISIONS pr on pr.ID = p.ID " +
                        "and pr.STATUS = 'RECEIVED' and pr.STATUS_MODIFIED = true " +
                    "inner join PICKLISTS_ITEMS pi2 on pi2.ID = p.TYPE_OF_PAYMENT_ID " +
                    "where p.CONTRACT_ID = ?1 and p.METHOD_OF_PAYMENT <> 'DIRECT_DEBIT' " +
                        "and pi2.CODE = 'monthly_payment' and p.STATUS = 'RECEIVED' " +
                        "and pr.LAST_MODIFICATION_DATE > ?2 " +
                    "order by p.DATE_OF_PAYMENT desc, pr.LAST_MODIFICATION_DATE asc")
    List<Long> getLastMonthReceivedDate(Long contractId, Date lastReset);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' and p.typeOfPayment.code = 'monthly_payment' " +
                "and YEAR(p.dateOfPayment) = ?2 and MONTH(p.dateOfPayment) = ?3")
    Boolean existsMonthlyPaymentReceivedByDate(Contract contract, int year, int month);

    @Query("select p " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' and p.typeOfPayment.code = 'monthly_payment' " +
                "and YEAR(p.dateOfPayment) = ?2 and MONTH(p.dateOfPayment) = ?3 ")
    List<Payment>  getPaymentAmountByYearAndMonth(Contract contract, int year, int month);

    @Query("select p from Payment p " +
           "where p.id > ?1 and p.requiredForBouncing = true and p.status = 'DELETED' order by id")
    Page<Payment> findByRequiredForBouncingAndStatusDeletedAndIdGreaterThan(Long lastId, Pageable pageable);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract.id = ?1 and p.typeOfPayment.code = 'monthly_payment' and p.status = 'RECEIVED'")
    Boolean existsMonthlyPaymentReceived(Long contractId);

    @Query("select p.amountOfPayment from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = 'monthly_payment' and p.status = 'RECEIVED' " +
            "order by p.dateOfPayment desc")
    List<Double> findAmountByLastMonthlyPaymentReceived(Contract c);

    @Query("select count(p.id) > 0 " +
            "from Payment p " +
            "where p.contract.id = :cId and p.typeOfPayment.code = 'monthly_payment' and " +
                "p.status = 'RECEIVED' and (:prorated is null or p.isProRated = :prorated)")
    Boolean existsMonthlyPaymentReceived(@Param("cId") Long contractId, @Param("prorated") Boolean isProrated);

    @Query("select p.status, p.amountOfPayment, p.typeOfPayment.name, p.methodOfPayment, p.typeOfPayment.code, " +
            "p.workerSalary, p.workerSalaryWithoutVAT, p.visaFees "+
            "from Payment p " +
            "where p.contract = ?1 and p.dateOfPayment between ?2 and ?3 and p.status <> 'DELETED' and " +
                "not exists (select 1 from PicklistItem pit join pit.tags t where pit.id = p.typeOfPayment.id and " +
                    "t.name = 'refund')  " +
            "order by case " +
                "when p.status = 'RECEIVED' then 0 " +
                "when p.status = 'PDC' then 1 " +
                "when p.status = 'BOUNCED' then 2 " +
                "when p.status = 'PRE_PDP' then 3 " +
            "else 4 end")
    List<Object[]> findCurrentMonthPaymentsInfo(Contract contract, Date d1, Date d2);

    @Query("select p.status, p.amountOfPayment, p.typeOfPayment.name, p.dateOfPayment, p.workerSalary " +
            "from Payment p " +
            "join p.typeOfPayment t " +
            "where p.contract = :contract and p.amountOfPayment > 0 and p.status <> 'DELETED' and " +
            "((:startDate is not null and :endDate is not null and t.code = 'monthly_payment' and p.dateOfPayment between :startDate and :endDate and " +
            "p.includeWorkerSalary = true and p.workerSalary > 0) or " +
            "t.code in ('pre_collected_payment', 'pre_collected_payment_no_vat')) " +
            "order by case " +
                "when p.status = 'RECEIVED' then 0 " +
                "when p.status = 'PDC' then 1 " +
                "when p.status = 'BOUNCED' then 2 " +
                "when p.status = 'PRE_PDP' then 3 " +
            "else 4 end, p.dateOfPayment asc")
    List<Object[]> findPreCollectedPaymentsInfo(
            @Param("contract") Contract contract, @Param("startDate") Date s, @Param("endDate")  Date e);

    @Query(nativeQuery = true, value =
            "SELECT COUNT(p.ID) " +
                    "FROM PAYMENTS p " +
                    "INNER JOIN PICKLISTS_ITEMS t ON p.TYPE_OF_PAYMENT_ID = t.ID " +
                    "WHERE p.CONTRACT_ID = ?1 AND p.AMOUNT_OF_PAYMENT = ?2 AND p.STATUS = 'RECEIVED' AND " +
                    "p.DATE_OF_PAYMENT >= ?3 AND p.DATE_OF_PAYMENT <= ?4 AND " +
                        "EXISTS (SELECT 1 FROM PICKLIST_ITEM_TAGS pit " +
                                "INNER JOIN TAGS t2 ON t2.ID = pit.TAG " +
                                "WHERE pit.ITEM = t.ID AND t2.NAME = ?5)")
    int countRefundsByContractAndPaymentTypeAndDate(Long contractId, Double amount, Date s, Date e, String tagName);

    @Query("select count(p.id) " +
            "from Payment p " +
            "where p.contract.id = ?1 and p.amountOfPayment = ?2 and p.dateOfPayment >= ?3 and p.dateOfPayment <= ?4 and " +
                "p.typeOfPayment.code = ?5 and p.status = 'RECEIVED'")
    int countReceivedPaymentsByContractAndTypeAndDate(Long contractId, Double amount, Date s, Date e, String type);

    @Query("select p.status " +
            "from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = ?2 " +
                "and p.dateOfPayment between ?3 and ?4 " +
                "and p.status <> 'DELETED' " +
            "order by p.creationDate desc")
    List<Object[]>  findStatusByContractAndTypeOfPaymentAndDateOfPayment(Contract contract, String code, Date d1, Date d2);

    @Query("Select p.typeOfPayment.code, p.dateOfPayment " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED'")
    List<Object[]> getAllReceivedPayment(Contract contract);

    @Query("Select count(p.id) > 0 " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED'")
    boolean existsReceivedPayment(Contract contract);

    @Query("select p.id, p.amountOfPayment, p.dateOfPayment " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' and p.dateOfPayment >= ?2 " +
                "and p.typeOfPayment.code = 'monthly_payment' and " +
            "not exists(select 1 from ContractPaymentWrapper w where w.generatedPaymentId = p.id and " +
                "w.contractPaymentConfirmationToDo.source = 'SWITCH_NATIONALITY') " +
            "order by p.creationDate desc")
    List<Object[]> getMonthlyPaymentForRefundAfterDownGradeNationality(Contract c, Date d);

    @Query("select p.id, p.amountOfPayment, p.dateOfPayment " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = ?2 and p.dateOfPayment >= ?3 and " +
                "p.typeOfPayment.code = 'monthly_payment' " +
            "order by p.creationDate desc")
    List<Object[]> findMonthlyPaymentByStatusAndDateOfPaymentGreaterThanEqual(Contract c, PaymentStatus s, Date d);

    boolean existsByDirectDebitIdAndStatus(Long directDebitId, PaymentStatus status);

    boolean existsByIdAndStatus(Long paymentId, PaymentStatus status);

    @Query("select count(p.id) > 0 " +
            "from Payment p " +
            "where p.contract = ?1 and p.amountOfPayment = ?2 and p.typeOfPayment.code = ?3 and " +
                "(p.status = 'RECEIVED' or p.replaced = true or " +
                    "(p.status ='DELETED' and " +
                        "not exists (select 1 " +
                                    "from Payment p1 " +
                                    "where p1.id <> p.id and p1.contract = ?1 and p1.amountOfPayment = ?2 and " +
                                        "p1.typeOfPayment.code = ?3 and " +
                                        "(p1.status not in ('RECEIVED', 'DELETED') and p1.replaced = false))))")
    Boolean existsPaymentMustCollectedManuallyForAcc6629(Contract contract, Double amount, String code);

    boolean existsByContractAndStatusAndReplaced(Contract contract, PaymentStatus status, boolean replaced);

    @Query("select distinct new map(p.id as paymentId, p.dateOfPayment as dateOfPayment, " +
                "todo.id as todoId, c.id as contractId, cl.id as clientId, cl.name as clientName) " +
            "from ContractPaymentWrapper cpw " +
            "inner join cpw.contractPaymentConfirmationToDo todo " +
            "inner join todo.contractPaymentTerm cpt " +
            "inner join cpt.contract c " +
            "inner join c.client cl " +
            "inner join Payment p on p.id = " +
                "case " +
                    "when cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId is not null " +
                        "then cpw.replacedFuturePaymentId " +
                    "when cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                            "exists (select 1 from Payment p1 where p1.replacementFor.id = cpw.replacedBouncedPaymentId) " +
                        "then (select max(id) from Payment p1 " +
                            "where p1.replacementFor.id = cpw.replacedBouncedPaymentId and p1.methodOfPayment = 'CARD') " +
                    "else cpw.generatedPaymentId " +
                "end " +
            "where todo.reactivationPayment = true and todo.payingOnline = true and todo.showOnERP = true and " +
            "todo.paymentMethod = 'CARD' and p.status = 'received' and todo.cardPaymentReceivedDate >= ?1 " +
            "group by p.id " +
            "order by p.dateOfPayment asc")
    List<Map> findAllReceivedPaymentPerToDo(Date date);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = 'monthly_payment' and " +
                "p.status <> 'DELETED' and p.isProRated = true and p.dateOfPayment <= ?2")
    Boolean existsMonthlyPaymentProRatedNotDeleted(Contract contract, Date d);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = 'monthly_payment' and " +
            "p.status <> 'DELETED' and p.isProRated = true and p.dateOfPayment between ?2 and ?3 ")
    Boolean existsMonthlyPaymentProRatedNotDeleted(Contract contract, Date startDate, Date endDate);

    @Query(nativeQuery = true,
            value = "SELECT p.ID FROM PAYMENTS p " +
                    "INNER JOIN PICKLISTS_ITEMS t ON t.ID = p.TYPE_OF_PAYMENT_ID " +
                    "WHERE p.CONTRACT_ID = ?1 AND p.STATUS = 'RECEIVED' AND " +
                    "NOT EXISTS (SELECT ID FROM PICKLIST_ITEM_TAGS pit INNER JOIN TAGS t2 ON t2.ID = pit.TAG " +
                    "WHERE pit.ITEM = t.ID AND t2.NAME = 'refund')")
    List<Object[]> findAllPaymentReceivedByContract(Long contractId);

    @Query("select p from Payment p " +
            "inner join DirectDebit d on d.id = p.directDebitId " +
            "inner join DirectDebitFile ddf on ddf.id = p.directDebitFileId " +
            "where p.id > ?1 and p.sentToBankByMDD = false and " +
                "p.status = 'PDC' and p.dateOfPayment <= ?2 and ddf.ddStatus = 'CONFIRMED' and ddf.ddMethod = 'MANUAL' and " +
                    "(ddf.ddaRefNo is null or ddf.ddaRefNo not like concat(?3 , '%')) and " + // ACC-2259, exclude auto OIC
                "not exists( select 1 from DirectDebitCancelationToDo dc " +
                        "where dc.directDebitFile = ddf and dc.stopped = false and dc.completed = false and dc.hidden = false)")
    Page<Payment> findForSendingApprovedManualDDToBankScheduledJob_PDC(Long lastId, Date d, String autoOIC, Pageable pageable);

    @Query("select p from Payment p " +
            "inner join DirectDebit d on d.id = p.directDebitId " +
            "inner join DirectDebitFile ddf on ddf.id = d.manualDdfFile.id " +
            "where p.id > ?1 and p.sentToBankByMDD = false and  p.status = 'BOUNCED' and " +
                "p.replaced = false and p.bouncedFlowPausedForReplacement = false and ddf.ddStatus = 'CONFIRMED' and " +
                    "(ddf.ddaRefNo is null or ddf.ddaRefNo not like concat(?2 , '%')) and " + // ACC-2259, exclude auto OIC
                "not exists( select 1 from DirectDebitCancelationToDo dc " +
                    "where dc.directDebitFile = ddf and dc.stopped = false and dc.completed = false and dc.hidden = false)")
    Page<Payment> findForSendingApprovedManualDDToBankScheduledJob_BOUNCED(Long lastId, String autoOIC, Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT DISTINCT P.ID " +
            "FROM PAYMENTS P " +
            "INNER JOIN BANKSTATEMENTRECORDS R ON R.DIRECT_DEBIT_FILE_ID = P.DIRECT_DEBIT_FILE_ID " +
            "WHERE R.ID IN (?1) AND P.STATUS IN ('BOUNCED', 'PDC') AND " +
                    "((YEAR(P.DATE_OF_PAYMENT) = YEAR(R.`DATE`) AND MONTH(P.DATE_OF_PAYMENT)= MONTH(R.`DATE`)) OR " +
                    "((YEAR(P.DATE_OF_PAYMENT) = YEAR(DATE_ADD(R.`DATE`,INTERVAL -1 MONTH)) AND MONTH(P.DATE_OF_PAYMENT)= MONTH(DATE_ADD(R.`DATE`,INTERVAL -1 MONTH))))) " +
            "ORDER BY P.DATE_OF_PAYMENT ASC")
    List<BigInteger> findMatchedPaymentForProcessDirectDebit(List<Long> recordIds);

    @Query("select p from Payment p " +
             "where p.contract.id = ?1 and p.status = 'RECEIVED' and p.typeOfPayment.code = ?2 " +
              "order by p.dateChangedToReceived desc")
    List<Payment> findLastPaymentPaidByContractAndPaymentType(Long contractId, String type);

    @Query("select distinct p.id, p.dateOfPayment, p.amountOfPayment, todo.transferReference " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "inner join Payment p on p.id = " +
                "case " +
                    "when cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId is not null " +
                        "then cpw.replacedFuturePaymentId " +
                    "when cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                            "exists (select 1 from Payment p1 where p1.replacementFor.id = cpw.replacedBouncedPaymentId) " +
                        "then (select max(id) from Payment p1 " +
                            "where p1.replacementFor.id = cpw.replacedBouncedPaymentId and p1.methodOfPayment = 'CARD') " +
                    "else cpw.generatedPaymentId " +
                "end " +
            "where p.contract.id = ?1 and p.methodOfPayment = 'card' and p.status = 'received' and " +
            "todo.payingOnline = true and todo.showOnERP = true and todo.source <> 'CLIENT_REFUND' " +
            "order by p.dateOfPayment desc")
    List<Object[]> findAllCardPaymentsByContract(Long contractId);

    @Query("select p from Payment p " +
            "where p.contract.id = ?1 and p.status = 'BOUNCED' and p.replaced = false and " +
                "p.typeOfPayment.code <> 'monthly_payment'")
    List<Payment> findAllNonMonthlyBouncedPaymentByContractId(Long contractId);

    @Query("select count(p) " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' and " +
            "p.typeOfPayment.code = 'monthly_payment' and p.amountOfPayment > 0")
    int findTheCountOfMonthlyPaymentsReceived(Contract c);

    @Query("select p " +
            "from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' and " +
            "p.typeOfPayment.code <> 'monthly_payment' and p.dateChangedToReceived between ?2 and ?3")
    List<Payment> getNonMonthlyPaymentReceivedDateBetween(Contract c, Date startDate, Date endDate);

    @Query("select p.dateOfPayment from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = 'monthly_payment' and p.status = ?2 " +
            "and p.dateOfPayment > ?3 " +
            "order by p.dateOfPayment asc")
    List<Date> getMonthlyPaymentAfterPaidEndDate(Contract contract, PaymentStatus status, Date PED, Pageable pageable);

    @Query("select sum(p.amountOfPayment) from Payment p " +
            "where p.id in ?1")
    Double sumAmountOfPaymentByIds(List<Long> ids);

    boolean existsByClientRefundToDo(ClientRefundToDo c);

    List<Payment> findByContractAndPrepareToRefund(Contract contract, boolean PrepareToRefund);

    List<Payment> findByContractAndDateOfPayment(Contract contract, Date dateofPayment);

    @Query("select p.id, p.status, p.methodOfPayment, p.typeOfPayment.name, p.dateOfPayment, p.replaced, p.dateChangedToReceived " +
            "from Payment p " +
            "where p.contract = ?1 and p.dateOfPayment >= ?2 and p.status in ('PDC', 'BOUNCED', 'RECEIVED') and " +
            "not exists (select 1 from PicklistItem pit join pit.tags t where pit.id = p.typeOfPayment.id and t.name = 'refund')" +
            "order by p.dateOfPayment asc")
    List<Object[]> findByContractAndCreationDate(Contract contract, Date date);

    @Query("select p from Payment p " +
            "where p.contract = :contract and p.amountOfPayment = :amount and p.replaced = false and p.status='BOUNCED' " +
            "order by p.dateOfPayment asc")
    List<Payment> findUnReplacedBouncedPaymentByContractAndAmount(@Param("contract") Contract contract, @Param("amount") Double amount);

    List<Payment> findByDirectDebitIdAndDirectDebitFileIdAndStatus(Long directDebit, Long directDebitFile, PaymentStatus status);

    List<Payment> findByDirectDebitFileIdAndStatus(Long directDebitFile, PaymentStatus status);

    @Query("select count(p.id) from Payment p " +
            "where p.contract = :c and p.status = :s " +
            "and p.typeOfPayment = :t and p.amountOfPayment >= 0 ")
    int countByContractAndTypeOfPaymentAndStatusAndAmountPositive
            (@Param("c") Contract contract, @Param("t") PicklistItem typeOfPayment,
             @Param("s") PaymentStatus status);

    //CM 1870
    @Query("select p from Payment p " +
            "where p.typeOfPayment.code <> 'monthly_payment' " +
            "and p.amountOfPayment > 0 AND p.contract = :contract and p.affectsPaidEndDate = true")
    List<Payment> findAllByContractAndAmountPositiveAndNotMonthlyPaymentAndAffectsPaidEndDate(@Param("contract") Contract contract);

    @Query("select p from Payment p " +
            "where p.contract = :c and p.status = :s " +
            "and p.typeOfPayment = :t and p.amountOfPayment >= 0 ")
    List<Payment> findByContractAndTypeOfPaymentAndStatusAndAmountPositive(@Param("c") Contract contract, @Param("t") PicklistItem typeOfPayment, @Param("s") PaymentStatus status );

    List<Payment> findByContractAndStatusAndDateOfPaymentGreaterThanAndIgnoreVATAndVatPaidByClientAndTypeOfPayment(
            Contract contract, PaymentStatus status,
            Date dateOfPayment, Boolean ignoreVAT,
            Boolean vatPaidByClient, PicklistItem typeOfPayment);

    @Query("select p from Payment p " +
            "where p.contract = ?1 and p.status not in ('DELETED', 'RECEIVED') and p.recurring = true")
    List<Payment> findByContractAndRecurringTrue(Contract contract);

    @Query("select count(p) > 0 from Payment p " +
            "where p.contract = ?1 and p.status not in ('DELETED', 'RECEIVED') " +
                "and p.recurring = true and p.dateOfPayment >= ?2")
    boolean existsByContractAndRecurringTrue(Contract contract, Date d);

    @Query("select count(p) > 0 from Payment p " +
            "where p.contract = ?1 and p.dateOfPayment between ?2 and ?3  and p.typeOfPayment.code = 'monthly_payment' and " +
            "p.recurring = true and p.amountOfPayment = ?4 and p.status <> 'DELETED'")
    Boolean existsByMonthAndContractAndRecurringTrue(Contract contract, Date startDate, Date endDate, Double amount);

    @Query("select count(p) > 0 from Payment p " +
            "where p.contract = ?1 and p.recurring = true and p.status = ?2 and " +
            "p.dateOfPayment between ?3 and ?4")
    Boolean existsRecurringPaymentByContractAndStatusAndDateBetween(Contract contract, PaymentStatus status, Date startDate, Date endDate);

    @Query("select p " +
            "from ContractPaymentTerm cpt " +
            "join cpt.contract c "+
            "inner join Payment p on p.contract = c " +
            "where c.status = 'ACTIVE' and cpt.id > ?1 and c.payingViaCreditCard = true and c.allowRecurring = true and " +
                "cpt.isActive = true and cpt.sourceId is not null and " +
                "p.recurring = true and p.status = ?2 and p.dateOfPayment <= ?3 and " +
                "not exists (select 1 from FlowProcessorEntity f " +
                              "where f.contractPaymentTerm = cpt and " +
                                "f.flowEventConfig.name = 'CLIENTS_PAYING_VIA_Credit_Card' and " +
                                "f.currentSubEvent.name in ?4 and f.creationDate >= p.dateOfPayment) and " +
                "not exists (select 1 from Payment p1 " +
                                    "where p1.contract = c and p1.status = 'RECEIVED' and " +
                                    "year(p1.dateOfPayment) = year(p.dateOfPayment) and " +
                                    "month(p1.dateOfPayment) = month(p.dateOfPayment) and p1.typeOfPayment.code = 'monthly_payment')")
    Page<Payment> findByCptAndFlowProcessEntityAndPayingViaCreditCardAndAllowRecurring(
            Long lastId, PaymentStatus status, Date currentDay,
            List<FlowSubEventConfig.FlowSubEventName> recurringFailureFlowsWithExpiredCard, Pageable pageable);

    @Query( "select new map(p as payment, cpt as cpt) " +
            "from Payment p " +
            "inner join ContractPaymentTerm cpt on cpt.contract = p.contract " +
            "where p.id > ?1 and cpt.isActive = true and p.typeOfPayment.code = 'monthly_payment' and " +
            "p.recurring = true and p.amountOfPayment <> cpt.sourceAmount and p.status not in ('DELETED', 'RECEIVED')")
    Page<Map<String, Object>> findRecurringPaymentAmountUpdated(Long lastId, Pageable pageable);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.dateOfPayment between ?2 and ?3 and " +
            "p.typeOfPayment.code = ?4 and p.status = 'RECEIVED'")
    Boolean existReceivedPaymentByContractAndDateOfPaymentBetween(
            Contract contract, Date s, Date e, String typeOfPayment);

    @Query("select p " +
            "from Payment p " +
            "join p.contract c " +
            "where c.id = ?1 and p.status not in ('DELETED', 'RECEIVED') and p.recurring = true and " +
                "p.typeOfPayment.code = 'monthly_payment' and p.dateOfPayment between ?2 and ?3 and p.amountOfPayment = ?4")
    List<Payment> findRecurringPaymentByContractAndPaymentDate(
            Long contractId, Date startDate, Date endDate, Double amount);

    @Query("select count(p.id) > 0 " +
            "from Payment p " +
            "where p.contract.id = ?1 and p.amountOfPayment = ?2 and p.dateOfPayment = ?3 and " +
            "p.typeOfPayment.code = ?4 and p.status = 'RECEIVED'")
    boolean existsReceivedPaymentsByContractAndAmountAndTypeAndDate(Long contractId, Double amount, Date date, String type);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = ?2 and p.status = 'RECEIVED'")
    boolean existsReceivedPaymentByContractAndType(Contract contract, String type);

    @Query("select p from Payment p " +
            "where p.id > ?1 and p.replaced = true and p.requiredForBouncing = true")
    Page<Payment> findByReplacedTrueAndRequiredForBouncingTrue(Long lastId, Pageable pageable);

    @Query("select p from Payment p " +
            "where p.contract = ?1 and p.methodOfPayment = 'CARD' and p.status = 'PDC' and p.typeOfPayment = ?2 and " +
                 "p.dateOfPayment between ?3 and ?4 and p.recurring = true")
    List<Payment> findByContractAndMethodOfPaymentAndStatusAndTypeOfPaymentAndDateOfPaymentAndRecurring(
            Contract contract, PicklistItem type, Date startDate, Date endDate);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.methodOfPayment = 'CARD' and p.status = 'PDC' and p.typeOfPayment = ?2 and " +
                "p.dateOfPayment between ?3 and ?4 and p.recurring = true")
    boolean existsByContractAndMethodOfPaymentAndStatusAndTypeOfPaymentAndDateOfPaymentAndRecurring(
            Contract contract, PicklistItem type, Date startDate, Date endDate);

    @Query("select distinct p from Payment p " +
            "inner join ContractPaymentWrapper cpw on " +
                "(cpw.generatedPaymentId = p.id or " +
                    "(cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId = p.id) or " +
                    "(cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                        "p.replacementFor.id = cpw.replacedBouncedPaymentId)) " +
            "inner join cpw.contractPaymentConfirmationToDo todo " +
            "where todo.id = ?1")
    List<Payment> findPaymentsByTodo(Long toDoId);

    @Query("select new Map (p.id as id, p.status as status, p.amountOfPayment as amount, p.typeOfPayment.name as type, p.replaced as replaced) " +
            "from Payment p " +
            "where p.contract.id = ?1 and p.dateOfPayment between ?2 and ?3 and p.status <> 'DELETED' and  " +
            "not exists (select 1 from PicklistItem pit join pit.tags t where pit.id = p.typeOfPayment.id and t.name = 'refund')")
    List<Map<String, Object>> findAllPaymentsByContractAndBetweenTwoDate(Long contractId, Date start, Date end);

    @Query("select p from Payment p " +
            "inner join DirectDebit d on d.id = p.directDebitId " +
            "where d.contractPaymentTerm.contract = ?1 and d.status not in ?2 and " +
            "d.category = 'B' and p.typeOfPayment.code = 'monthly_payment' and p.status not in ('DELETED', 'RECEIVED') and " +
            "p.dateOfPayment >= ?3 and p.dateOfPayment <= ?4")
    List<Payment> findFirstDDBPaymentByContractAndStatusNotInAndDateBetween(Contract c, List<DirectDebitStatus> l, Date s, Date e);

    @Query("select pr from Payment pr " +
            "join pr.contract c " +
            "inner join ContractPaymentTerm cpt on cpt.contract = pr.contract and cpt.isActive = true  " +
            "where pr.id > ?1 and c.startOfContract <= ?2 and  pr.recurring = true and pr.status = 'PDC' and pr.dateOfPayment between ?3 and ?4 and " +
                "c.contractProspectType.code = 'maidvisa.ae_prospect' and c.allowRecurring = true and " +
                "c.status = 'ACTIVE' and c.housemaid is not null and " +
                "cpt.sourceId is not null and cpt.sourceInfo is not null and cpt.sourceAmount is not null and " +
                "exists (select 1 from Payment p " +
                            "where p.contract = c and p.amountOfPayment > 0 and p.status = 'RECEIVED' and " +
                                "p.typeOfPayment.code in ('pre_collected_payment', 'pre_collected_payment_no_vat')) and " +
                "exists (select 1 from BaseAdditionalInfo b " +
                                "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                                    "b.infoKey = 'preCollectedSalary' and b.infoValue = 'true') and " +
                "not exists (select 1 from BaseAdditionalInfo b " +
                                "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                                    "b.infoKey = 'passedMedicalStep' and b.infoValue = 'true') and " +
                "not exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.housemaid.id and b.ownerType = 'Housemaid' and " +
                        "b.infoKey = 'hasMedicalCheckThisMonth' and b.infoValue = ?5) " +
            "order by pr.id asc")
    Page<Payment> findRecurringContractsWithPreCollectdPaymentsForMedicalCheck(
            Long id, Date start, Date s, Date e, String lastPassedDate, Pageable pageable);

    @Query("select pr from Payment pr " +
            "join pr.contract c " +
            "inner join ContractPaymentTerm cpt on cpt.contract = pr.contract and cpt.isActive = true  " +
            "inner join ContractPaymentType t on t.contractPaymentTerm = cpt " +
            "where pr.id > ?1 and c.startOfContract <= ?2 and pr.recurring = true and pr.status = 'PDC' and pr.dateOfPayment between ?3 and ?4 and " +
                    "t.discountEffectiveAfter < ?5 and " +
                    "c.contractProspectType.code = 'maidvisa.ae_prospect' and c.allowRecurring = true and " +
                    "c.status = 'ACTIVE' and c.housemaid is not null and t.type.code = 'monthly_payment' and " +
                    "cpt.sourceId is not null and cpt.sourceInfo is not null and cpt.sourceAmount is not null and " +
            "exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                            "b.infoKey = 'preCollectedSalary' and b.infoValue = 'true') and " +
            "not exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                        "b.infoKey = 'passedMedicalStep' and b.infoValue = 'true') and " +
            "not exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.housemaid.id and b.ownerType = 'Housemaid' and " +
                        "b.infoKey = 'hasMedicalCheckThisMonth' and b.infoValue = ?6) " +
            "order by pr.id asc")
    Page<Payment> findAllRecurringContractsForMedicalCheck(Long id, Date start, Date s, Date e, int threshold, String lastPassedDate, Pageable pageable);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.dateOfPayment between ?2 and ?3 and p.amountOfPayment > 0 and p.status = 'RECEIVED' and " +
                        "p.typeOfPayment.code in ('pre_collected_payment', 'pre_collected_payment_no_vat')")
    Boolean existsReceivedPreCollectedPaymentByContractAndDateOfPaymentBetween(
            Contract contract, Date startDate, Date endDate);

    @Query("select min(p.dateOfPayment) from Payment p " +
            "where p.contract = ?1 and p.status = 'RECEIVED' and p.amountOfPayment > 0 and " +
                "(p.typeOfPayment.code in ('pre_collected_payment', 'pre_collected_payment_no_vat') or " +
                "(p.typeOfPayment.code = 'monthly_payment' and p.dateOfPayment between ?2 and ?3 and " +
                "p.includeWorkerSalary = true and p.workerSalary > 0))")
    Date findFirstPreCollectedOrMonthlyWithSalaryDate(Contract contract, Date s, Date e);

    @Query("select count(p.id) > 0 from Payment p " +
            "where p.contract = ?1 and p.typeOfPayment.code = 'monthly_payment' and p.status <> 'DELETED' AND " +
            "(p.methodOfPayment <> 'DIRECT_DEBIT' or p.status = 'RECEIVED' or not exists(" +
                "select 1 from DirectDebitCancelationToDo toDo " +
                "where toDo.directDebitFile.id = p.directDebitFileId and " +
                    "toDo.stopped = false and toDo.completed = false))")
    boolean existsActiveMonthlyPayment(Contract contract);

    @Query("select new Map(p.id as id, t.code as paymentTypeCode, p.amountOfPayment as amount) " +
            "from Payment p " +
            "join p.typeOfPayment t " +
            "where p.contract = ?1 and p.amountOfPayment > 0 and p.status = 'RECEIVED' and t.code in ?2")
    List<Map<String, Object>> findAllPaymentsReceivedByContractAndTypes(Contract contract, List<String> typeCodes);

    @Query("select count(p.id) > 0 " +
            "from Payment p " +
            "where p.contract.id = ?1 and p.status = 'RECEIVED' and " +
            "exists (select 1 from PicklistItem pit join pit.tags t where pit.id = p.typeOfPayment.id and t.name = ?2)")
    boolean existsReceivedPaymentsByContractAndTagName(Long contractId, String tag);

    @Query("select new map(p.id as id, p.methodOfPayment as methodOfPayment) " +
            "from Payment p " +
            "where p.contract = ?1 and p.dateOfPayment = ?2 and " +
            "p.typeOfPayment.code = 'monthly_payment' and p.status in ('PDC', 'PRE_PDP')")
    List<Map<String, Object>> findMonthlyPaymentsByContractAndDate(Contract contract, Date paymentDate);
}