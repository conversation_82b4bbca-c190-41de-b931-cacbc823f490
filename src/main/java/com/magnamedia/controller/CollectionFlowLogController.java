package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.EnableSwaggerMethod;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.JobDefinition;
import com.magnamedia.core.entity.JobInstance;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.master.repository.JobDefinitionRepository;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.CollectionFlowJobHistory;
import com.magnamedia.entity.CollectionFlowLog;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.DTOs.OngoingCollectionsFlowRequestDto;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.module.type.CollectionJobStatus;
import com.magnamedia.repository.CollectionFlowJobHistoryRepository;
import com.magnamedia.repository.CollectionFlowLogRepository;
import com.magnamedia.service.CollectionFlowLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 3/5/2022
 **/

@RestController
@RequestMapping("/collectionFlowLog")
public class CollectionFlowLogController extends BaseRepositoryController <CollectionFlowLog> {

    //picklistitems flow type
    public static final String PICKLISTITEM_BOUNCED_PAYMENT_FLOW = "Bounced payment flow";
    public static final String PICKLISTITEM_BOUNCED_PAYMENT_FLOW_CODE = "bounced_payment_flow";
    public static final String PICKLISTITEM_DD_REJECTION_FLOW = "DD Rejection Flow";
    public static final String PICKLISTITEM_DD_REJECTION_FLOW_CODE = "dd_rejection_flow";
    public static final String PICKLISTITEM_MISSING_DOCUMENT_FLOW = "Incomplete flow / Data entry rejection";
    public static final String PICKLISTITEM_INCOMPLETE_FLOW_DATA_ENTRY_REJECTION = "missing/wrong_document_flow";
    public static final String PICKLISTITEM_SWITCHING_NATIONALITY_FLOW = "Switching Nationality Flow";
    public static final String PICKLISTITEM_SWITCHING_NATIONALITY_FLOW_CODE = "switching_nationality_flow";
    public static final String PICKLISTITEM_SWITCHING_BANK_ACCOUNT_FLOW = "Switching Bank Account Flow";
    public static final String PICKLISTITEM_SWITCHING_BANK_ACCOUNT_FLOW_CODE = "switching_bank_account_flow";
    public static final String PICKLISTITEM_REFUND_FLOW = "Refund flow";
    public static final String PICKLISTITEM_REFUND_FLOW_CODE = "refund_flow";
    public static final String PICKLISTITEM_IPAM_FLOW = "Initial Payment By Alternative Method Flow";
    public static final String PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW = "Online Payments Reminder Flow";
    public static final String PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW_CODE = "online_payments_reminder_flow";
    public static final String PICKLISTITEM_IPAM_FLOW_CODE = "initial_payment_by_alternative_method_flow";
    public static final String PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE = "client_paying_via_credit_card_flow";
    public static final String PICKLISTITEM_ONE_MONTH_AGREEMENT_CODE = "one_month_agreement_flow";
    public static final String PICKLISTITEM_PAYMENT_EXPIRY_FLOW_CODE = "payment_expiry_flow";
    public static final String PICKLISTITEM_INCOMPLETE_FLOW_MISSING_BANK_INFO = "incomplete_flow_missing_bank_info";
    public static final String PICKLISTITEM_EXTENSION_FLOW = "extension_flow";

    // notes types
    public static final String SIGNATURE_REJECTION_REASON_NOTES = "The client signature has been rejected and client has to sign again";
    public static final String AUTHORIZATION_REJECTION_REASON_NOTES = "There's an authorization issue with the client's bank account, and the client needs to contact the bank account and approve the dds";
    public static final String INVALID_ACCOUNT_REJECTION_REASON_NOTES = "The currency of the client's account is not AED, the client needs to change the IBAN to an AED IBAN";
    public static final String EID_REJECTION_REASON_NOTES = "There's a mistake in the Emirates ID of the bank account holder that's submitted by the client.";
    public static final String ACCOUNT_REJECTION_REASON_NOTES = "There's a mistake in the bank account holder name and it doesn't match the one saved in bank.";

    @Autowired
    private CollectionFlowLogRepository collectionFlowLogRepository;
    @Autowired
    private CollectionFlowJobHistoryRepository collectionFlowJobHistoryRepository;
    @Autowired
    private JobDefinitionRepository jobDefinitionRepository;
    @Autowired
    private CollectionFlowLogService collectionFlowLogService;

    @Override
    public BaseRepositoryParent<CollectionFlowLog> getRepository() {
        return collectionFlowLogRepository;
    }

    @PreAuthorize("hasPermission('collectionFlowLog','getRunningCollectionFlowsDetailsByContract')")
    @RequestMapping("/getRunningCollectionFlowsDetailsByContract/{id}")
    public Map<String, Object> getRunningCollectionFlowsDetailsByContract(@PathVariable("id")Contract contract) {
        Map<String,Object> resultMap = new HashMap<>();

        Integer count = collectionFlowLogRepository.countCollectionFlowLogsByContract(contract);
        resultMap.put("count", count);

        JobDefinition collectionFlowLogJobDefinition = jobDefinitionRepository.findByModuleAndCode(Setup.getCurrentModule(), "CollectionFlowLogJob");
        SelectQuery<JobInstance> selectQuery = new SelectQuery<>(JobInstance.class);
        selectQuery.filterBy("definition", "=", collectionFlowLogJobDefinition);
        selectQuery.filterBy("active", "=", true);
        selectQuery.sortBy("id", false);
        List<JobInstance> jobInstances = selectQuery.execute();
        JobInstance collectionFlowLogJobInstance = jobInstances.size() > 0 ? jobInstances.get(0) : null;
        Date nextRunDate = collectionFlowLogJobInstance != null ? collectionFlowLogJobInstance.getNextRunDate() : null;
        resultMap.put("nextRunDate", nextRunDate);

        return resultMap;
    }

    @PreAuthorize("hasPermission('collectionFlowLog','getRunningCollectionFlowsDetailsByClient')")
    @RequestMapping("/getRunningCollectionFlowsDetailsByClient/{id}")
    public Map<String, Object> getRunningCollectionFlowsDetailsByClient(@PathVariable("id") Client client) {
        Map<String,Object> resultMap = new HashMap<>();

        Integer count = collectionFlowLogRepository.countCollectionFlowLogsByClient(client);
        resultMap.put("count", count);

        JobDefinition collectionFlowLogJobDefinition = jobDefinitionRepository.findByModuleAndCode(Setup.getCurrentModule(), "CollectionFlowLogJob");
        SelectQuery<JobInstance> selectQuery = new SelectQuery<>(JobInstance.class);
        selectQuery.filterBy("definition", "=", collectionFlowLogJobDefinition);
        selectQuery.filterBy("active", "=", true);
        selectQuery.sortBy("id", false);
        List<JobInstance> jobInstances = selectQuery.execute();
        JobInstance collectionFlowLogJobInstance = jobInstances.size() > 0 ? jobInstances.get(0) : null;
        Date nextRunDate = collectionFlowLogJobInstance != null ? collectionFlowLogJobInstance.getNextRunDate() : null;
        resultMap.put("nextRunDate", nextRunDate);

        CollectionFlowJobHistory lastSucceedCollectionFlowJobHistory = collectionFlowJobHistoryRepository.findTopByStatusOrderByIdDesc(CollectionJobStatus.SUCCEED);
        Date lastSucceedRunDate = lastSucceedCollectionFlowJobHistory!= null ? lastSucceedCollectionFlowJobHistory.getCalculateTo() : null;
        resultMap.put("lastSucceedRunDate", lastSucceedRunDate);

        Map<Long, Integer> contractsMap = new HashMap<>();
        for (Contract contract : client.getAllContracts()) {
            contractsMap.put(contract.getId(), collectionFlowLogRepository.countCollectionFlowLogsByContract(contract));
        }
        resultMap.put("contracts", contractsMap);

        return resultMap;
    }

    @PreAuthorize("hasPermission('collectionFlowLog','getRunningCollectionFlowsLogsByContract')")
    @RequestMapping("/getRunningCollectionFlowsLogsByContract/{id}")
    public ResponseEntity<?> getRunningCollectionFlowsLogsByContract(@PathVariable("id")Contract contract, Pageable pageable) {
        return ResponseEntity.ok(collectionFlowLogRepository.findByContractAndEndedFalse(contract, pageable));
    }

    @PreAuthorize("hasPermission('collectionFlowLog','getOngoingCollectionFlows')")
    @GetMapping("/getOngoingCollectionFlows/{id}")
    public ResponseEntity<?> getOngoingCollectionFlows(@PathVariable("id") Contract contract) {
        return ResponseEntity.ok(collectionFlowLogService.getRunningFlowsMessagesByContractACC8311(contract));
    }

    @EnableSwaggerMethod
    @UsedBy(others = UsedBy.Others.New_GPT)
    @GetMapping("/getOngoingCollectionsFlowAcc9872")
    public ResponseEntity<?> getOngoingCollectionsFlowAcc9872(
            @RequestParam(value = "contractId") Long contractId,
            @RequestParam(value = "flowCodes", required = false) String flowCodes) {

        Map<String, String> flowCodeMapping = new HashMap<>();
        flowCodeMapping.put("bouncing", "bounced_payment_flow");
        flowCodeMapping.put("ddRejection", "dd_rejection_flow");
        flowCodeMapping.put("dataEntryRejection", "missing/wrong_document_flow");
        flowCodeMapping.put("switchingNationality", "switching_nationality_flow");
        flowCodeMapping.put("switchingBankAccount", "switching_bank_account_flow");
        flowCodeMapping.put("refund", "refund_flow");
        flowCodeMapping.put("onlineReminder", "online_payments_reminder_flow");
        flowCodeMapping.put("ipam", "initial_payment_by_alternative_method_flow");
        flowCodeMapping.put("payingViaCC", "client_paying_via_credit_card_flow");
        flowCodeMapping.put("payingViaCCOMA", "one_month_agreement_flow");
        flowCodeMapping.put("paymentExpiry", "payment_expiry_flow");
        flowCodeMapping.put("missingBankInfo", "incomplete_flow_missing_bank_info");
        flowCodeMapping.put("extension", "extension_flow");
        
        List<String> flowIds = null;
        List<Object[]> flowStatuses;
        
        if (flowCodes != null && !flowCodes.trim().isEmpty()) {
            List<String> mappedFlowCodes = Arrays.asList(flowCodes.split(","))
                    .stream()
                    .map(code -> code.trim())
                    .map(code -> flowCodeMapping.getOrDefault(code, code))
                    .collect(java.util.stream.Collectors.toList());
            
            flowIds = mappedFlowCodes;
            flowStatuses = collectionFlowLogRepository.getFlowStatusByContractIdWithFilter(contractId, mappedFlowCodes);
        } else {
            flowStatuses = collectionFlowLogRepository.getFlowStatusByContractId(contractId);
        }

        if (flowIds != null && !flowIds.isEmpty()) {
            Map<String, Boolean> r = new HashMap<>();

            Map<String, String> reverseMapping = new HashMap<>();
            for (Map.Entry<String, String> entry : flowCodeMapping.entrySet()) {
                reverseMapping.put(entry.getValue(), entry.getKey());
            }

            for (String originalFlowCode : Arrays.asList(flowCodes.split(","))) {
                r.put(originalFlowCode.trim(), false);
            }

            for (Object[] result : flowStatuses) {
                String mappedFlowCode = (String) result[0];
                String originalFlowCode = reverseMapping.getOrDefault(mappedFlowCode, mappedFlowCode);
                r.put(originalFlowCode, true);
            }
            return ResponseEntity.ok(r);
        } else {
            Map<String, String> reverseMapping = new HashMap<>();
            for (Map.Entry<String, String> entry : flowCodeMapping.entrySet()) {
                reverseMapping.put(entry.getValue(), entry.getKey());
            }
            
            List<String> runningFlows = flowStatuses.stream()
                    .map(result -> (String) result[0])
                    .map(mappedFlowCode -> reverseMapping.getOrDefault(mappedFlowCode, mappedFlowCode))
                    .collect(java.util.stream.Collectors.toList());
            if (runningFlows.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            return ResponseEntity.ok(runningFlows);
        }
    }
}
