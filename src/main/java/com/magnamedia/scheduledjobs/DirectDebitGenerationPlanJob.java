package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.ContractPaymentTypeRepository;
import com.magnamedia.repository.DirectDebitGenerationPlanRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.sql.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

//ACC-3741
public class DirectDebitGenerationPlanJob implements MagnamediaJob {

    private final DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;
    private final ContractPaymentTypeRepository contractPaymentTypeRepository;
    private final FlowProcessorEntityRepository flowProcessorEntityRepository;

    private final DirectDebitGenerationPlanService directDebitGenerationPlanService;

    private final DirectDebitService directDebitService;

    private final FlowProcessorService flowProcessorService;
    private final ContractService contractService;
    private final UnpaidOnlineCreditCardPaymentService unpaidOnlineCreditCardPaymentService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitGenerationPlanJob.class.getName());

    public DirectDebitGenerationPlanJob() {
        directDebitGenerationPlanRepository = Setup.getRepository(DirectDebitGenerationPlanRepository.class);
        contractPaymentTypeRepository = Setup.getRepository(ContractPaymentTypeRepository.class);
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        directDebitGenerationPlanService = Setup.getApplicationContext().getBean(DirectDebitGenerationPlanService.class);
        directDebitService = Setup.getApplicationContext().getBean(DirectDebitService.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        contractService = Setup.getApplicationContext().getBean(ContractService.class);
        unpaidOnlineCreditCardPaymentService = Setup.getApplicationContext().getBean(UnpaidOnlineCreditCardPaymentService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    public void runJob() {

        logger.info("Start");

        directDebitGenerationPlanService.updatePlansUponActiveCptChanged();
        generationPostponedDDsScheduleByContracts();
        //ACC-8875
        sendNotificationForGeneratedDdFromPlans();
        generateDdFromPlans();
        sendReport();

        logger.info("End");
    }

    private void generationPostponedDDsScheduleByContracts() {
        logger.info("started");

        Long lastId = -1L;
        Date today = new Date(new java.util.Date().getTime());

        Page<ContractPaymentTerm> p;
        do {
            p = directDebitGenerationPlanRepository.findActiveCptHasNotActivePlan(
                    lastId, AbstractPaymentTypeConfig.monthlyTypes, today,
                    DirectDebitGenerationPlanService.pendingStatus,
                    PageRequest.of(0, 200));
            if (p.isEmpty()) break;

            p.getContent()
                    .forEach(cpt -> {
                        logger.info("cpt id: " + cpt.getId());
                        try {
                            if (contractService.hasPreventCreateOtherDds(cpt.getContract())) {
                                logger.info("Has Prevent Create Other Dds -> exiting");
                                return;
                            }

                            // ACC-9733
                            if (!flowProcessorEntityRepository
                                    .existsByFlowEventConfig_NameAndContractPaymentTerm_Contract(
                                            FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED, cpt.getContract())) {
                                if (!flowProcessorService.isPayingViaCreditCard(cpt.getContract()) &&
                                        !directDebitService.contractHasClosedMainDdcToDo(
                                                cpt.getContract().getId(), AppsServiceDDApprovalTodo.DdcTodoType.PROSPECT_APP)) {
                                    logger.info("Has not a closed main DDC todo or the IPAM flow or Paying Via CC flow is running -> exiting");
                                    return;
                                }
                            }

                            DateTime startDate = new DateTime(cpt.getContract().getStartOfContract()).withTimeAtStartOfDay();
                            DateTime endDate = startDate.plusMonths(cpt.getContract().getPaymentsDuration() - (cpt.getContract().getIsProRated() ? 0 : 1))
                                    .dayOfMonth().withMinimumValue();
                            cpt.getContractPaymentTypes()
                                    .stream()
                                    .filter(t -> !t.isPostponedDdGenerated() && !AbstractPaymentTypeConfig.monthlyTypes.contains(t.getType().getCode()))
                                    .forEach(t -> {
                                        generationNewPlanAndContractPaymentOfContractPaymentType(cpt, t, startDate, endDate);
                                        t.setPostponedDdGenerated(true);
                                        contractPaymentTypeRepository.silentSave(t);
                                    });
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });

            lastId = p.getContent().get(p.getContent().size() - 1).getId();

        } while (!p.isEmpty());

        logger.info("end");
    }

    private void generationNewPlanAndContractPaymentOfContractPaymentType(
            ContractPaymentTerm cpt, ContractPaymentType t, DateTime startDate, DateTime endDate) {
        logger.info("non Monthly ContractPaymentType id: " + t.getId());

        // 1- Get plans and contractPayment
        Map<String, Object> plansMap = directDebitGenerationPlanService.buildDirectDebitGenerationPlans(t, startDate, endDate);

        // 2- Create new Reminder Flow If contains Contract Payment and not exists Payment Received Or Covered Contract Payment
        if (plansMap.containsKey("contractPayment")) {

            ContractPayment contractPayment = (ContractPayment) plansMap.get("contractPayment");

            if (contractPayment.getDate().getTime() >= new LocalDate().dayOfMonth().withMinimumValue().toDate().getTime() &&
                    !directDebitGenerationPlanService.existsPaymentReceivedOrCoveredContractPayment(
                    cpt.getContract(), contractPayment.getPaymentType().getCode(),
                    new LocalDate(contractPayment.getDate()).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate(contractPayment.getDate()).dayOfMonth().withMaximumValue().toDate())) {
                unpaidOnlineCreditCardPaymentService
                        .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
                                Collections.singletonList(contractPayment), cpt);
            }
        }

        // 3- Generation New Plan Of Contract Payment Type
        List<DirectDebitGenerationPlan> plans = (List<DirectDebitGenerationPlan>) plansMap.get("plans");
        logger.info("plans size: " + plans.size());

        plans.forEach(plan ->
                directDebitGenerationPlanService.saveGenerationPlan(plan, new HashMap<>()));
    }

    private void sendNotificationForGeneratedDdFromPlans() {
        logger.info("Start");
        String daysNum = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DAYS_BEFORE_SEND_NOTIFICATION_FOR_GENERATION_PLAN_INSURANCE);
        if (daysNum == null || daysNum.isEmpty()) return;

        Long lastId = -1L;
        Date afterXDays = new Date(new LocalDate().plusDays(Integer.parseInt(daysNum)).toDate().getTime());
        Page<DirectDebitGenerationPlan> page;
        do {
            page = directDebitGenerationPlanRepository.findByStatusPendingAndTypeInsuranceAndDDGenerationDateEquals(
                    lastId, afterXDays, PageRequest.of(0, 100));

            for (DirectDebitGenerationPlan plan : page.getContent()) {
                try {
                    directDebitGenerationPlanService.sendNotificationAndStartReminderFlow(plan);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (!page.getContent().isEmpty()) {
                lastId = page.getContent().get(page.getContent().size() - 1).getId();
            }

        } while (!page.getContent().isEmpty());

        logger.info("End");
    }

    private void generateDdFromPlans() {
        logger.log(Level.INFO, "Start");
        List<Long> processedPlanIDs = new ArrayList<>();
        processedPlanIDs.add(-1L);
        Date today = new Date(new LocalDate().toDate().getTime());
        Page<DirectDebitGenerationPlan> page;

        int pageIndex = 0;
        do {
            page = directDebitGenerationPlanRepository.findByStatusPendingAndDDGenerationDateEquals(
                    today, processedPlanIDs, PageRequest.of(pageIndex, 100));

            for (DirectDebitGenerationPlan directDebitGenerationPlan : page.getContent()) {
                try {
                    processedPlanIDs.add(directDebitGenerationPlan.getId());
                    logger.log(Level.INFO, "Start BK task Generate direct debits "
                            + "plan Id " + directDebitGenerationPlan.getId());

                    Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                            .create(new BackgroundTask.builder(
                                    "generateDDFromGenerationPlan" + new java.util.Date().getTime(),
                                    "accounting",
                                    "directDebitGenerationPlanService",
                                    "generateDDFromGenerationPlan")
                                    .withRelatedEntity("DirectDebitGenerationPlan", directDebitGenerationPlan.getId())
                                    .withParameters(
                                            new Class[] {Long.class},
                                            new Object[] {directDebitGenerationPlan.getId()})
                                    .withQueue(BackgroundTaskQueues.SequentialQueue)
                                    .withDelay(directDebitGenerationPlan.getOneTime() ? 0L : 60L * 1000L)
                                    .build());

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "exception while generate direct debits running on " +
                            "directDebitGenerationPlan#" + directDebitGenerationPlan.getId());
                    logger.log(Level.SEVERE, "Exception: " + ExceptionUtils.getStackTrace(e));
                }
            }
        } while (page.hasNext());

        logger.log(Level.INFO, "End");
    }

    private void sendReport() {
        logger.log(Level.INFO, "Start");
        Date today = new Date(new LocalDate().toDate().getTime());
        //Send a report to George
        List<DirectDebitGenerationPlan> directDebitGenerationPlans =
                directDebitGenerationPlanRepository.findByStatusPendingAndDDGenerationDateLessThan(today);
        if (directDebitGenerationPlans != null && directDebitGenerationPlans.size() > 0) {
            logger.log(Level.INFO, "Plans size : " + directDebitGenerationPlans.size());
            directDebitGenerationPlanService.sendFailedDDsGenerationReport(directDebitGenerationPlans);
        }

        logger.log(Level.INFO, "End send a report");
    }
}