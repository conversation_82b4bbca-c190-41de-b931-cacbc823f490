package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.magnamedia.controller.*;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.chatai.ChatAIRequestBuilder;
import com.magnamedia.core.helper.epayment.EPaymentProvider;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.services.chatai.ChatAIService;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.DirectDebitSalesProjection;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.entity.workflow.VoiceResolverToDo;
import com.magnamedia.extra.*;
import com.magnamedia.helper.CcAppContentHelper;
import com.magnamedia.helper.ConcurrentModificationHelper;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.module.type.VoiceResolverToDoReason;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitARejectionWaitingClientReSignStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBCaseDRejectionWaitingClientReSignStep;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.controller.ContractPaymentTermController.FILE_TAG_PAYMENTS_RECEIPT;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 4/13/2022
 **/

@Service
public class ContractPaymentTermServiceNew {
    private static final Logger logger = Logger.getLogger(ContractPaymentTermServiceNew.class.getName());

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private InterModuleConnector connector;
    @Autowired
    private ContractRepository contractRep;
    @Autowired
    private AttachementRepository attachementRepository;
    @Autowired
    private PicklistItemRepository picklistItemRepository;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRep;
    @Autowired
    private ContractPaymentTermHelper contractPaymentTermHelper;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private Utils utils;
    @Autowired
    private DirectDebitController directDebitCtrl;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractPaymentController contractPaymentController;
    @Autowired
    private DirectDebitCancelationToDoController directDebitCancelationToDoController;
    @Autowired
    private SwitchingBankAccountService switchingBankAccountService;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private ClientRepository clientRepository;
    @Autowired
    AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRep;
    @Autowired
    private ContractPaymentTermExtendRepository contractPaymentTermExtendRepository;
    @Autowired
    private DDMsgConfigRepository directDebitMessagesConfigRepository;
    @Autowired
    private QueryService queryService;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private ContractPaymentService contractPaymentService;
    @Autowired
    private SwitchingNationalityService switchingNationalityService;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private ChatAIService chatAIService;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private MessagingService messagingService;
    @Autowired
    private ContractService contractService;

    //this is a new Function to create Cash Payments individually
    public void addCashPayments (
            ContractPaymentTerm contractPaymentTerm,
            List<LinkedHashMap> cashPayments,
            String authorizationCode,
            String transferReference,
            String oldContracts,
            boolean ignoreSendNotification) throws Exception {

        if (cashPayments != null && !cashPayments.isEmpty()) {
            contractPaymentService.savePaymentsMapList(contractPaymentTerm, cashPayments, authorizationCode, transferReference, oldContracts, ignoreSendNotification);
        }
    }

    public ResponseEntity<?> signDDByClientAsyncBGT(
            Map<String, Object> payload,
            String contractUUID,
            String eid,
            String iban,
            String account,
            Boolean eidPhotoChanged,
            Boolean ibanPhotoChanged,
            Boolean accountNamePhotoChanged,
            Boolean useOldSignature,
            Boolean needToSign,
            List<LinkedHashMap> cashPayments,
            Long ibanRejectionReasonId,
            Long accountNameRejectionReasonId,
            Long eidRejectionReasonId,
            Boolean pendingOcr) throws Exception {

        return signDDByClientAsyncBGT(payload,
                contractUUID, eid, iban,
                account, eidPhotoChanged, ibanPhotoChanged,
                accountNamePhotoChanged, useOldSignature,
                needToSign, cashPayments, ibanRejectionReasonId,
                accountNameRejectionReasonId, eidRejectionReasonId,
                pendingOcr, new HashMap<>());
    }

    public ResponseEntity<?> signDDByClientAsyncBGT(
            Map<String, Object> payload,
            String contractUUID,
            String eid,
            String iban,
            String account,
            Boolean eidPhotoChanged,
            Boolean ibanPhotoChanged,
            Boolean accountNamePhotoChanged,
            Boolean useOldSignature,
            Boolean needToSign,
            List<LinkedHashMap> cashPayments,
            Long ibanRejectionReasonId,
            Long accountNameRejectionReasonId,
            Long eidRejectionReasonId,
            Boolean pendingOcr,
            Map<String, Object> m) throws Exception {

        Contract contract = getContractFromPayload(payload, contractUUID);

        Map<String, Object> bankInfo = fillAttatchemnts(payload);
        Attachment eidPhoto = (Attachment) bankInfo.get("eidPhoto");
        Attachment ibanPhoto = (Attachment) bankInfo.get("ibanPhoto");
        Attachment accountPhoto = (Attachment) bankInfo.get("accountPhoto");
        List<Attachment> signatureList = (List<Attachment>) bankInfo.get("signatureList");


        PicklistItem ibanRejectionReason = null, accountNameRejectionReason = null, eidRejectionReason = null;

        if (ibanRejectionReasonId != null) {
            ibanRejectionReason = picklistItemRepository.findOne(ibanRejectionReasonId);
        }

        if (accountNameRejectionReasonId != null) {
            accountNameRejectionReason = picklistItemRepository.findOne(accountNameRejectionReasonId);
        }

        if (eidRejectionReasonId != null) {
            eidRejectionReason = picklistItemRepository.findOne(eidRejectionReasonId);
        }

        signDDByClient(contractUUID, contract, eidPhoto, ibanPhoto, accountPhoto,
                eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                eid, iban, account,
                signatureList, useOldSignature, needToSign, cashPayments,
                ibanRejectionReason, accountNameRejectionReason, eidRejectionReason,
                pendingOcr,
                m);

        return new ResponseEntity("signing DD is being processed", HttpStatus.OK);
    }

    // ACC-6385
    public Contract getContractFromPayload(Map<String, Object> payload, String contractUUID) {
        if ((!payload.containsKey("id") || payload.get("id") == null) &&
                Utils.isEmpty(contractUUID))
            throw new RuntimeException("Not found");

        Long contractId;
        Contract contract = null;

        if (payload.containsKey("id")) {
            if (payload.get("id") instanceof Integer)
                contractId = new Long((Integer) payload.get("id"));
            else
                contractId = (Long) payload.get("id");

            contract = contractRep.findOne(contractId);
        }

        return contract;
    }

    // ACC-6385
    public Map<String, Object> fillAttatchemnts(Map<String, Object> payload) {

        Map<String, Object> result = new HashMap<String, Object>() {{
            put("eidPhoto", null);
            put("ibanPhoto", null);
            put("accountPhoto", null);
            put("signatureList", null);
        }};
        List<Attachment> signatureList = new ArrayList<>();

        List<Map<String, Object>> mapList = payload.containsKey("attachments") && payload.get("attachments") != null ?
                (List<Map<String, Object>>) payload.get("attachments") :
                new ArrayList<>();

        for (Map<String, Object> map : mapList) {
            Long attachmentId;
            if (map.get("id") instanceof Integer)
                attachmentId = new Long((Integer) map.get("id"));
            else
                attachmentId = (Long) map.get("id");

            Attachment a = attachementRepository.findOne(attachmentId);

            if (a.getTag().toLowerCase().contains("eid")) result.put("eidPhoto", a);
            if (a.getTag().toLowerCase().contains("iban")) result.put("ibanPhoto", a);
            if (a.getTag().toLowerCase().contains("account")) result.put("accountPhoto", a);
            if (a.getTag().toLowerCase().contains("signature")) signatureList.add(a);
        }

        result.put("signatureList", signatureList);
        return result;
    }

    public Map<String, Object> getContractInfo(
            ContractPaymentTerm cpt,
            boolean contractExtend,
            int extendDuration,
            boolean isCash) throws Exception {

        logger.log(Level.INFO, "cpt ID: {0}", cpt.getId());

        // 8- update contract payment term
        if (contractExtend && extendDuration > 0) {
            DirectDebit lastDD = directDebitRepository.getPendingOrConfimDDs(cpt, PageRequest.of(0, 1))
                    .get(0);
            DateTime startDate = new DateTime(lastDD.getExpiryDate()).withDayOfMonth(1).plusMonths(1);
            DateTime endDate = startDate.plusMonths(extendDuration - 1);
            addNewDD(cpt.getContract(), startDate.toDate(), endDate.toDate(), null, null, null, null, null,
                    DirectDebitType.MONTHLY, null, false, null, false, true, true, null, true);
        }

        Map termResult = getContractPaymentTermByContractWithDirectDebitPayments(cpt);

        List<DirectDebit> allDirectDebits = ((List<DirectDebit>) termResult.get("directDebits"))
                .stream()
                .filter(dd -> !isCash || !DateUtil.isSameDate(
                        dd.getStartDate(), cpt.getContract().getStartOfContract()))
                .collect(Collectors.toList());
        List<DirectDebit> toBeSignedDirectDebits = new ArrayList();
        List<ContractPayment> oneTimePayments = new ArrayList();
        List<Map> directDebits = new ArrayList();

        // Acc-1778 deduct worker salary
        boolean mvContract = cpt.getContract().getContractProspectType().getCode()
                .equalsIgnoreCase(AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE);

        Double workerSalary = cpt.getContract().getWorkerSalaryWithoutVat();
        boolean deductWorkerSalary = mvContract && workerSalary != null;

        Date startDiscountDate = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(cpt)).toDate();

        allDirectDebits.forEach(directDebit -> {
            boolean discountedDD =
                    new DateTime(directDebit.getStartDate()).withTimeAtStartOfDay().toDate().getTime() >=
                    startDiscountDate.getTime();

            if (directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE) ||
                    directDebit.getMStatus().equals(DirectDebitStatus.IN_COMPLETE) ||
                    (Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY)
                            .contains(directDebit.getStatus()) &&
                            (directDebit.getNonCompletedInfo() || (directDebit.getId() != null &&
                                    directDebitFileRepository.existsByDirectDebitAndStatus(
                                            directDebit, DirectDebitFileStatus.NOT_COMPLETED))))) {

                List<ContractPayment> ddPayments = directDebit.getPayments();

                if (ddPayments != null) {
                    StringBuilder log = new StringBuilder();
                    ddPayments.stream().filter(p -> p.getId() == null)
                            .forEach(p -> calculateDiscountsWithVatService.updateVatFields(p, log));
                    if (!log.toString().isEmpty()) {
                        logger.info(log.toString());
                    }
                }

                if (!directDebit.getType().equals(DirectDebitType.ONE_TIME) ||
                        ddPayments == null || ddPayments.isEmpty()) {

                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
                    objectMapper.setDateFormat(DateUtil.getDashedDateFormat());

                    Map dd = objectMapper.convertValue(projectionFactory.
                            createProjection(DirectDebitSalesProjection.class, directDebit), Map.class);

                    if (directDebit.getType().equals(DirectDebitType.MONTHLY)) {
                        if (ddPayments != null && !ddPayments.isEmpty()) {
                            ContractPayment firstPayment = ddPayments.get(0);
                            logger.info( "firstPayment.getPaymentWithoutVAT(): " + firstPayment.getPaymentWithoutVAT() +
                                    "; deductWorkerSalary: " + deductWorkerSalary +
                                    "; discountedDD: " + discountedDD +
                                    "; workerSalary: " + workerSalary);

                            dd.put("deductWorkerSalary", deductWorkerSalary && discountedDD);
                            dd.put("amount", firstPayment.getPaymentWithoutVAT() - (deductWorkerSalary && discountedDD ? workerSalary : 0));
                            dd.put("withVat", firstPayment.getVat() > 0);
                        } else {
                            dd.put("withVat", false);
                        }
                    }

                    directDebits.add(dd);
                } else {
                    ddPayments.forEach(p -> {
                        if (PaymentHelper.isMonthlyPayment(p.getPaymentType()) && deductWorkerSalary && discountedDD) {
                            p.setPaymentWithoutVAT(p.getPaymentWithoutVAT() - workerSalary);
                        }
                    });
                    oneTimePayments.addAll(ddPayments);
                }

                toBeSignedDirectDebits.add(directDebit);
            }
        });

        Map<String, Object> result = new HashMap();
        result.put("directDebits", directDebits);
        result.put("oneTimePayments", oneTimePayments);
        result.put("toBeSignedDirectDebits", toBeSignedDirectDebits);
        result.put("nextPayment", ((List<ContractPayment>) termResult.get("payments")).stream()
                .filter(p -> !paymentRepository.existsByContractAndAmountOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetweenAndStatus(
                        cpt.getContract(), p.getAmount(), p.getPaymentType().getCode(),
                        new LocalDate(p.getDate()).withDayOfMonth(1).toDate(),
                        new LocalDate(p.getDate()).dayOfMonth().withMaximumValue().toDate(),
                        PaymentStatus.RECEIVED))
                .findFirst().orElse(null));

        return result;
    }

    @Transactional
    public ResponseEntity<?> signDDByClient(
            String contractUUID, Contract contract,
            Attachment eidPhoto, Attachment ibanPhoto, Attachment accountPhoto,
            Boolean eidPhotoChanged, Boolean ibanPhotoChanged, Boolean accountNamePhotoChanged,
            String eid, String iban, String account,
            List<Attachment> signatures,
            Boolean useOldSignature,
            Boolean needToSign,
            List<LinkedHashMap> cashPayments,
            PicklistItem ibanRejectionReason,
            PicklistItem accountNameRejectionReason,
            PicklistItem eidRejectionReason) throws Exception {


        return signDDByClient(contractUUID, contract, eidPhoto, ibanPhoto, accountPhoto,
            eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
            eid, iban, account,
            signatures, useOldSignature, needToSign, cashPayments,
            ibanRejectionReason, accountNameRejectionReason, eidRejectionReason, false);
    }

    @Transactional
    public ResponseEntity<?> signDDByClient(
            String contractUUID, Contract contract,
            Attachment eidPhoto, Attachment ibanPhoto, Attachment accountPhoto,
            Boolean eidPhotoChanged, Boolean ibanPhotoChanged, Boolean accountNamePhotoChanged,
            String eid, String iban, String account,
            List<Attachment> signatures,
            Boolean useOldSignature,
            Boolean needToSign, List<LinkedHashMap> cashPayments,
            PicklistItem ibanRejectionReason, PicklistItem accountNameRejectionReason, PicklistItem eidRejectionReason,
            boolean pendingOcr) throws Exception {

        return signDDByClient(contractUUID, contract, eidPhoto, ibanPhoto, accountPhoto,
                eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                eid, iban, account, signatures,
                useOldSignature, needToSign, cashPayments,
                ibanRejectionReason, accountNameRejectionReason,
                eidRejectionReason, pendingOcr , new HashMap<>());

    }

    @Transactional
    public ResponseEntity<?> signDDByClient(
            String contractUUID, Contract contract,
            Attachment eidPhoto, Attachment ibanPhoto, Attachment accountPhoto,
            Boolean eidPhotoChanged, Boolean ibanPhotoChanged, Boolean accountNamePhotoChanged,
            String eid, String iban, String account,
            List<Attachment> signatures,
            Boolean useOldSignature,
            Boolean needToSign, List<LinkedHashMap> cashPayments,
            PicklistItem ibanRejectionReason, PicklistItem accountNameRejectionReason, PicklistItem eidRejectionReason,
            boolean pendingOcr,
            Map<String, Object> m) throws Exception {

        logger.log(Level.SEVERE, "SIGN DD BY CLIENT entering sign dd by client");

        boolean clientMode = !Utils.isEmpty(contractUUID);
        if(contract == null && clientMode) contract = contractRep.findByUuid(contractUUID);
        if (contract == null) throw new RuntimeException("Contract not found");

        if (contract.getWaivedMonths() > 0 && (contract.isMaidCc() || contract.getIsProRated())) {
            throw new BusinessException("Waived payments are supported only for non-prorated MV contract");
        }

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);

        // 0- Add waived payments before start generate DDs ACC-7050
        if (contract.isMaidVisa() && contract.getWaivedMonths() > 0) {
            Setup.getApplicationContext()
                    .getBean(PaymentService.class)
                    .addWaivedPayment(contract);
        }

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);

        contractService.updatePaidEndDate(contract);

        // 1- getting active CPT
        ContractPaymentTerm cpt = null;
        Map termResult = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (termResult != null) cpt = (ContractPaymentTerm) termResult.get("contractPaymentTerm");
        if(cpt == null) throw new RuntimeException("No active contractPaymenterm");

        logger.log(Level.INFO, "cpt ID: {0}", cpt.getId());
        ContractPaymentTerm contractPaymentTerm = contractPaymentTermRep.findOne(cpt.getId());

        if(contractPaymentTerm.getPaymentTermConfig() != null) {
            logger.log(Level.INFO, "cpt config: {0}; weekly payment template: {1}",
                    new Object[]{contractPaymentTerm.getPaymentTermConfig().getId(),
                            contractPaymentTerm.getPaymentTermConfig().getWeeklyPaymentTermsTemplate()});
        }

        // ACC-8662
        // Check if Should be run Prevent Create Other Dds
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .validateAndStartPreventCreateOtherDds(cpt);

        // 2- checking signatures
        if (clientMode && needToSign && !useOldSignature &&
                (signatures == null || signatures.isEmpty())) {

            contractPaymentTermHelper.sendClientSignError(contract);
        }

        // jira acc-1542
        // 3- set rejected files flag to not rejected since client has uploaded new photos
        contractPaymentTermHelper.changeRejectedFilesFlag(contractPaymentTerm,
                eidPhoto != null ? utils.getInputStreamFromAttachmentOrMultiPartFile(eidPhoto) : null,
                ibanPhoto != null ? utils.getInputStreamFromAttachmentOrMultiPartFile(ibanPhoto) : null,
                accountPhoto != null ? utils.getInputStreamFromAttachmentOrMultiPartFile(accountPhoto) : null);

        // 4- update term bank info
        if (pendingOcr) { //ACC-4657
            contractPaymentTermHelper.saveAttachmentsPendingOcr(
                    contractPaymentTerm, eidPhoto, ibanPhoto, accountPhoto);
        } else {
            contractPaymentTermHelper.extractBankInfoByOCR(contractPaymentTerm, eidPhoto, ibanPhoto, accountPhoto,
                eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                eid, iban, account);
        }


        // 5- check all bank info provided
        if (clientMode && !directDebitService.isRequiredBankInfoExist(contractPaymentTerm)) {
            throw new RuntimeException("Please enter all required bank info");
        }

        // 6- check if contract is postponed
        if (contract.getStatus().equals(ContractStatus.POSTPONED)) {
            if (needToSign) {
                signDdPostponedContract(contractPaymentTerm, signatures, useOldSignature);
            } else {
                contractPaymentTermRep.save(contractPaymentTerm);
            }
            return new ResponseEntity<>(HttpStatus.OK);
        }

        // 7- add cash payments
        if (cashPayments != null && !cashPayments.isEmpty()) {
            contractPaymentService.savePaymentsMapList(contractPaymentTerm, cashPayments, null, null);
        }

        // 8- update contract payment term -> REMOVED

        logger.log(Level.SEVERE, "before selecting dds with rejection todo");

        List<DirectDebit> ddWithRejectionTodoAndInComplete =
                directDebitRepository.findByStatusAndDirectDebitRejectionToDoNotNullAndContractPaymentTerm(DirectDebitStatus.IN_COMPLETE, contractPaymentTerm);
        logger.log(Level.SEVERE, "ddWithRejectionTodoAndInComplete size: " +
                (ddWithRejectionTodoAndInComplete != null ? ddWithRejectionTodoAndInComplete.size() : null));

        List<DirectDebit> ddWithBouncingRejectionTodo =
                directDebitRepository.findByDirectDebitBouncingRejectionToDoNotNullAndContractPaymentTerm(contractPaymentTerm);
        logger.log(Level.SEVERE, "ddWithBouncingRejectionTodo size: " +
                (ddWithBouncingRejectionTodo != null ? ddWithBouncingRejectionTodo.size() : null));

        // 9- CLOSE TODOS
        closeResolverToDos(contract);

        List<Long> beforeSigningDDsIDs = new ArrayList();
        if (contractPaymentTerm.getReason() != null &&
                contractPaymentTerm.getReason().equals(ContractPaymentTermReason.SWITCHING_BANK_ACCOUNT) &&
                signatures != null && !signatures.isEmpty()) {

            List<DirectDebitStatus> invalidStatuses = Arrays.asList(
                    DirectDebitStatus.CANCELED, DirectDebitStatus.PENDING_FOR_CANCELLATION);

            beforeSigningDDsIDs.addAll(directDebitRepository.findByContractPaymentTermAndStatusIn(
                            contractPaymentTerm, Arrays.asList(DirectDebitStatus.IN_COMPLETE), invalidStatuses)
                    .stream().map(dd -> {
                        logger.log(Level.INFO, "DD#" + dd.getId() + ", will be signed");
                        return dd.getId();
                    }).collect(Collectors.toList()));

            logger.log(Level.INFO, "Switching Bank Account");
            logger.log(Level.INFO, "In_Complete DDs, " + beforeSigningDDsIDs);
        }

        // 10- FETCH PAYMENTS, DDS, PLANS
        if(contract.isPayingViaCreditCard()) deleteCreditCardToken(cpt, false);

        if (!contract.isOneMonthAgreement() && contract.isPayingViaCreditCard() && directDebitRepository.hasDdb(contractPaymentTerm)) {

            termResult = Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class)
                    .getFutureDirectDebitAndContractPayment(contractPaymentTerm);
        } else {
            termResult = getContractPaymentTermByContractWithDirectDebitPayments(contractPaymentTerm);
        }

        // 11- SAVE PAYMENTS AND GENERATE DDS
        List<ContractPayment> payments = (List<ContractPayment>) termResult.get("payments");
        updateContractPaymentTermWithPayments(contractPaymentTerm, true, useOldSignature,
                payments, signatures, true, !needToSign, true,
                false, false, true, pendingOcr, m);

        if (contract.isOneMonthAgreement() &&
                ((cashPayments != null && !cashPayments.isEmpty()) ||
                contractPaymentRep.existsNonDDContractPayment(contract, "monthly_payment"))) {

            logger.info("contract id: " + contract.getId() + "; isOneMonthAgreement: " + contract.isOneMonthAgreement());
            Setup.getApplicationContext()
                    .getBean(OneMonthAgreementFlowService.class)
                    .flowStoppedResetFlag(contract);
        }

        if (ddWithRejectionTodoAndInComplete != null) {
            for (DirectDebit directDebit : ddWithRejectionTodoAndInComplete) {

                logger.log(Level.SEVERE, "SIGN DD BY CLIENT directDebit id: " + directDebit.getId());

                DirectDebitRejectionToDo rejectionTodo = directDebit.getDirectDebitRejectionToDo();

                logger.log(Level.SEVERE, "SIGN DD BY CLIENT rejectionTodo id: " + rejectionTodo.getId());

                List<String> currentTasks = rejectionTodo.getCurrentTasks();
                if (!currentTasks.isEmpty()) {
                    if (currentTasks.size() > 0 && currentTasks.get(currentTasks.size() - 1) != null && !currentTasks.get(currentTasks.size() - 1).isEmpty()) {
                        if (rejectionTodo.isCompleted()) {
                            rejectionTodo.setCompleted(false);
                        }
                        if (rejectionTodo.isStopped()) {
                            rejectionTodo.setStopped(false);
                        }
                        if (rejectionTodo.getContractScheduleDateOfTermination() != null) {
                            rejectionTodo.setContractScheduleDateOfTermination(null);
                        }
                        DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                        logger.log(Level.SEVERE, "SIGN DD BY CLIENT step is:" + step);
                        switch (step) {
                            case WAITING_CLIENT_SIGNATURE: {
                                DirectDebitARejectionWaitingClientReSignStep clientReSignStep = Setup.getApplicationContext().getBean(DirectDebitARejectionWaitingClientReSignStep.class);
                                clientReSignStep.onDone(rejectionTodo);
                                break;
                            }
                            case WAITING_CLIENT_SIGNATURE_B_CASE_D: {
                                DirectDebitBCaseDRejectionWaitingClientReSignStep clientReSignStep = Setup.getApplicationContext().getBean(DirectDebitBCaseDRejectionWaitingClientReSignStep.class);
                                clientReSignStep.onDone(rejectionTodo);
                                break;
                            }
                        }
                    } else {
                        logger.log(Level.SEVERE, "SIGN DD BY CLIENT negative condition");
                    }
                } else {
                    logger.log(Level.SEVERE, "SIGN DD BY CLIENT currentTasks.isEmpty");
                }
            }
        }

        DirectDebitRejectionToDoRepository ddRejectTodoRepo = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
        if (ddWithBouncingRejectionTodo != null) {
            for (DirectDebit directDebit : ddWithBouncingRejectionTodo) {
                logger.log(Level.SEVERE, "SIGN DD BY CLIENT bouncing directDebit id: " + directDebit.getId());

                DirectDebitRejectionToDo directDebitBouncingRejectionToDo = directDebit.getDirectDebitBouncingRejectionToDo();
                if (directDebitBouncingRejectionToDo != null) {
                    directDebitBouncingRejectionToDo.setStopped(true);
                    directDebitBouncingRejectionToDo.setCompleted(true);
                    ddRejectTodoRepo.save(directDebitBouncingRejectionToDo);
                }
            }
        }

        //Jirra ACC-2428
        // if any DDF already generated for Bounced Payment, and not CONFIRMED yet, then cancel the DDFs
        if (beforeSigningDDsIDs != null && !beforeSigningDDsIDs.isEmpty()) {
            logger.log(Level.INFO, "Switching Bank Account, After Signing");
            List<DirectDebit> toMarkAsForBouncing = new ArrayList();
            List<Payment> bouncedPaymentsToSwap = new ArrayList();
            List<DirectDebit> generatedDDs = directDebitRepository.findByIdIn(beforeSigningDDsIDs)
                    .stream().filter(dd -> !dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE) && !dd.getMStatus().equals(DirectDebitStatus.IN_COMPLETE))
                    .collect(Collectors.toList());
            logger.log(Level.INFO, "Generated DDs Size:" + generatedDDs.size());
            boolean isPendingDDs = generatedDDs.stream().anyMatch(dd -> dd.getStatus().equals(DirectDebitStatus.PENDING));

            for (DirectDebit oldDD : generatedDDs.stream().filter(dd -> dd.getImageForDD() != null).map(dd -> dd.getImageForDD()).collect(Collectors.toList())) {
                logger.log(Level.INFO, "Checking Old DD:" + oldDD.getId());
                List<Payment> ddBouncedPayments = paymentRepository.findByDirectDebitIdAndStatusAndReplaced(oldDD.getId(), PaymentStatus.BOUNCED, false);
                logger.log(Level.INFO, "ddBouncedPayments Size:" + ddBouncedPayments.size());
                if (ddBouncedPayments != null && !ddBouncedPayments.isEmpty() &&
                        oldDD.getManualDdfFile() == null &&
                        oldDD.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())) {
                    logger.log(Level.INFO, "Checked" + oldDD.getId());
                    toMarkAsForBouncing.add(oldDD);
                    //bouncedPaymentsToSwap.addAll(ddBouncedPayments);

                    List<Long> ddfIDsToCancel = oldDD.getDirectDebitFiles().stream().filter(ddf -> ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                            .map(directDebitFile -> directDebitFile.getId()).collect(Collectors.toList());

                    for (Long ddfID : ddfIDsToCancel) {
                        Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                            .cancelDDf(ddfID, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                    }

                    if (isPendingDDs) {
                        bouncedPaymentsToSwap.addAll(ddBouncedPayments);
                    }
                }
            }
            switchingBankAccountService.markNewDDFsAsForBouncingPayment(toMarkAsForBouncing);

            for (Payment payment : bouncedPaymentsToSwap) {
                Map requestBody = new HashMap();
                requestBody.put("bouncedPaymentId", payment.getId());
                moduleConnector.postJsonAsync("accounting/contractpaymentterm/switchingBankAccount/swapBouncedPayment", requestBody);
            }

        }
        contractPaymentTermHelper.closeRelatedNotificationsForDDSinging(contract);
        contractPaymentTermHelper.closeRelatedDDMessages(contract);
        // ACC-2476
        List<String> tags = new ArrayList<>();
        if (ibanRejectionReason != null) {
            logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, ibanRejectionReason: " + ibanRejectionReason.getId());
            tags.add(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);
        } else {
            logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, ibanRejectionReason is null");
        }
        if (accountNameRejectionReason != null) {
            logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, accountNameRejectionReason: " + accountNameRejectionReason.getId());
            tags.add(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME);
        } else {
            logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, accountNameRejectionReason is null");
        }
        if (eidRejectionReason != null) {
            logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, eidRejectionReason: " + eidRejectionReason.getId());
            tags.add(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
        } else {
            logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, eidRejectionReason is null");
        }

        logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, tags size: " + tags.size());

        if (!tags.isEmpty()) {
            logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, contractPaymentTerm id: " + contractPaymentTerm.getId());

            List<DirectDebit> dds = directDebitRepository.findByContractPaymentTerm(contractPaymentTerm);

            if (dds != null && dds.size() > 0) {
                logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, dds size: " + dds.size());
                List<DirectDebitFile> directDebitFiles = null;
                for (int i = 0; i < dds.size(); i++) {
                    logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, directDebit id: " + dds.get(i).getId());
                    directDebitFiles = directDebitFileRepository.findByDirectDebit_Id(dds.get(i).getId());
                    if (directDebitFiles != null && !directDebitFiles.isEmpty()) {
                        logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, directDebitFiles is not empty");
                        break;
                    }
                }

                if (directDebitFiles != null && !directDebitFiles.isEmpty()) {
                    DirectDebitFile directDebitFile = directDebitFiles.get(0);

                    logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, directDebitFile id: " + directDebitFile.getId());

                        Setup.getApplicationContext().getBean(DirectDebitService.class)
                                .removeDdBankPhoto(directDebitFile, tags,
                                        ibanRejectionReason, eidRejectionReason, accountNameRejectionReason,
                                        true);
                } else {
                    logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, directDebitFiles is null or empty");
                }
            } else {
                logger.log(Level.SEVERE, "SIGN DD BY CLIENT ABD, dds is null or empty");
            }
        }

        return ResponseEntity.ok("Done");
    }

    @Transactional
    public void signDdPostponedContract(
            ContractPaymentTerm contractPaymentTerm, List<Attachment> signatures,
            Boolean useOldSignature) throws IOException {

        // add signatures to contract payment term
        if (signatures == null ||  signatures.isEmpty()) {
            // check if there is old signature
            if (useOldSignature) {
                Map<String, Object> signatureType = directDebitSignatureService.getLastSignatureType(
                        contractPaymentTerm, true, false);
                signatures = directDebitSignatureService.getSignatureAttachmentsOnly(
                        (List<DirectDebitSignature>) signatureType.get("currentSignatures"));
                if (signatures == null || signatures.isEmpty())
                    throw new RuntimeException("There are no previous approved/pending signatures");
            } else {
                throw new RuntimeException("please provid the signatures");
            }
        }
        int i = 0;
        for (Object o : signatures) {
            Attachment a = Storage.storeTemporary(ContractPaymentTermController.FILE_TAG_POSTPONED_SIGNATURE + i, utils
                    .getInputStreamFromAttachmentOrMultiPartFile(o), ContractPaymentTermController.FILE_TAG_POSTPONED_SIGNATURE + i, true, true);
            contractPaymentTerm.addAttachment(a);
            i++;
        }
        contractPaymentTermRep.save(contractPaymentTerm);
        // send sms to the client after signing
        Client client = contractPaymentTerm.getContract().getClient();
        Map<String, String> param = new HashMap<>();
        String link1 = utils.shorteningUrl(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
                + "/modules/sales/vat-app/#!/postponed-mv-agreements?uid=" + contractPaymentTerm.getContract().getUuid() + "&action=proceed");
        param.put("client_first_name_with_title", client.getFirstName(true));
        param.put("client_nickname_or_first_name", StringUtils.getClientNicknameOrFirstName(client));
        param.put("link", link1);
        messagingService.sendClientSms(contractPaymentTerm.getContract(),
                        TemplateUtil.getTemplate("Postponed_MV_After_Signing"),
                        param,
                        new HashMap<>(),
                        client.getNormalizedMobileNumber(),
                        client.getNormalizedWhatsappNumber(),
                        client.getId(),
                        client.getEntityType());
    }

    public ResponseEntity<?> updateContractPaymentTermWithPayments(
            ContractPaymentTerm updatedContractPaymentTerm,
            Boolean generateDD, Boolean useOldSignatures, List<ContractPayment> payments,
            List<Attachment> signatures, Boolean informClient, Boolean ignoreDDSignature,
            boolean savePayments, boolean ignoreRejectedSignatures,
            boolean ignoreRejectionDDs, boolean withIncomplete,
            boolean forceDataEntry) throws Exception {

       return updateContractPaymentTermWithPayments(
                 updatedContractPaymentTerm, generateDD, useOldSignatures,  payments,
                 signatures, informClient, ignoreDDSignature, savePayments,  ignoreRejectedSignatures,
                 ignoreRejectionDDs,  withIncomplete, forceDataEntry, new HashMap<>());
    }

    public ResponseEntity<?> updateContractPaymentTermWithPayments(
            ContractPaymentTerm updatedContractPaymentTerm,
            Boolean generateDD, Boolean useOldSignatures, List<ContractPayment> payments,
            List<Attachment> signatures, Boolean informClient, Boolean ignoreDDSignature,
            boolean savePayments, boolean ignoreRejectedSignatures,
            boolean ignoreRejectionDDs, boolean withIncomplete,
            boolean forceDataEntry, Map<String, Object> m) throws Exception {

        if (updatedContractPaymentTerm.getBankName() != null &&
                !updatedContractPaymentTerm.getBankName().isEmpty() &&
                updatedContractPaymentTerm.getBank() == null)
            throw new RuntimeException("The bank is required");


        // Remove link CPT with DDC if DDC is CLOSED_WITH_NO_CONFIRMATION
        if (updatedContractPaymentTerm.getDdcId() != null && !Setup.getApplicationContext()
                .getBean(AppsServiceDDApprovalTodoService.class)
                .existsApprovalAttachmentsByDDC(updatedContractPaymentTerm.getDdcId())) {

            updatedContractPaymentTerm.setDdcId(null);
            m.remove("ddcId");
        }

        updatedContractPaymentTerm = contractPaymentTermRep.save(updatedContractPaymentTerm);

        if (!generateDD) {
            if (updatedContractPaymentTerm.getSpouseWillSignDD()) {
                sendDDInfoMessages(updatedContractPaymentTerm.getContract(), true, true);
            }
            return new ResponseEntity("Done", HttpStatus.OK);
        }

        // save payments
        for (ContractPayment payment : payments) {
            if (payment == null) {
                logger.info("DDs were deleted");
                return new ResponseEntity("DDs were deleted", HttpStatus.NOT_FOUND);
            }
            payment.setContractPaymentTerm(updatedContractPaymentTerm);
        }

        directDebitCtrl.generateDD(payments, signatures, updatedContractPaymentTerm, useOldSignatures,
                ignoreDDSignature, savePayments, false, withIncomplete,
                ignoreRejectionDDs, forceDataEntry, m);

        // generate payments receipt
        Attachment contractSignature = updatedContractPaymentTerm.getContract().getAttachments()
                .stream()
                .filter(att -> att.getTag().equals(ContractController.SIGNATURE_FILE_TAG_NAME))
                .findFirst()
                .orElse(null);

        InputStream signatureForReceipt = null;
        try {
            signatureForReceipt = contractSignature != null ?
                    Storage.getStream(contractSignature) : (signatures == null || signatures.isEmpty() ?
                    null : utils.getInputStreamFromAttachmentOrMultiPartFile(signatures.get(0)));

            // new block for payment details email signature
            if (signatureForReceipt == null) {
                Map<String, Object> result = directDebitSignatureService.getLastSignatureType(updatedContractPaymentTerm, true, false);

                if (result != null && result.containsKey("currentSignatures") && result.get("currentSignatures") != null
                        && (result.get("currentSignatures") instanceof List) && !((List<Attachment>) result.get("currentSignatures")).isEmpty()) {

                    contractSignature = ((List<DirectDebitSignature>) result.get("currentSignatures")).get(0).getSignatureAttachment();
                    signatureForReceipt = Storage.getStream(contractSignature);
                }
            }

            generateAndSavePaymentsReceipt(updatedContractPaymentTerm, null, null, signatureForReceipt, false);
            if (informClient)
                sendDDFilesToClient(updatedContractPaymentTerm.getContract(), null, updatedContractPaymentTerm.getSpouseWillSignDD());

            return new ResponseEntity("done", HttpStatus.OK);
        } finally {
            StreamsUtil.closeStream(signatureForReceipt);
        }
    }

    public ResponseEntity<?> sendDDFilesToClient(
            Contract contract, Client client, boolean sendToSpouse) {

        // SAL-2034
        List<ContractPaymentTerm> cpts = contractPaymentTermRep.findByContractAndIsActiveOrderByCreationDateDesc(contract, true);
        ContractPaymentTerm cpt = null;

        boolean haveToSendReceiptFile = true;
        if (cpts != null && !cpts.isEmpty()) {
            cpt = cpts.get(0);

            // ACC-2694
            if (cpt.getReceiptSent() != null && cpt.getReceiptSent()) {
                logger.info("Receipt Already sent to the client");
                haveToSendReceiptFile = false;
            }
        }

        HistorySelectQuery<Contract> historyQuery = new HistorySelectQuery(Contract.class);
        historyQuery.filterBy("id", "=", contract.getId());
        historyQuery.filterBy("status", "=", ContractStatus.POSTPONED);
        List<Contract> contracts = historyQuery.execute();

        boolean postponedPassed = (contracts != null && !contracts.isEmpty());
        if (client == null) client = contract.getClient();

        String clientName = client.getName();
        String clientMobileNumber = client.getMobileNumber();
        if (clientMobileNumber == null || clientMobileNumber.isEmpty())
            return new ResponseEntity("Client has no email or mobile number",
                    HttpStatus.BAD_REQUEST);

        Map result = getDirectDebitFiles(contract, false);
        Attachment receiptAttachment = (Attachment) result.get("paymentsReceiptAttachment");
        String dearClientNameSms = clientName == null || clientName.isEmpty() ?
                "" : "Dear " + client.getFirstName(true);

        // check maid visa agreement file
        String agreementMVContractURL = null;
        boolean haveToSendMaidVisaAgreementFile = false;
        if (contract.getContractProspectType().getCode()
                .equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {

            AccountingEntityProperty maidVisaAgreementFileSentByMail =
                    accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                            ContractController.MAID_VISA_AGREEMENT_FILE_SENT_BY_MAIL, contract);

            haveToSendMaidVisaAgreementFile =
                    maidVisaAgreementFileSentByMail == null
                            && !contract.getIsMaidVisaServiceApplication()
                            && contract.getAttachments() != null
                            && contract.getAttachments().stream().anyMatch(
                            attachment -> attachment.getTag().equalsIgnoreCase(ContractController.FILE_TAG_MV_CONTRACT_AGREEMENT));

            if (haveToSendMaidVisaAgreementFile) {
                maidVisaAgreementFileSentByMail = new AccountingEntityProperty();
                maidVisaAgreementFileSentByMail.setOrigin(contract);
                maidVisaAgreementFileSentByMail.setKey(ContractController.MAID_VISA_AGREEMENT_FILE_SENT_BY_MAIL);
                maidVisaAgreementFileSentByMail.setValue("true");
                accountingEntityPropertyRepository.save(maidVisaAgreementFileSentByMail);
            }
        }
        if ((receiptAttachment == null || !haveToSendReceiptFile)
                && !haveToSendMaidVisaAgreementFile)
            return new ResponseEntity("No files to be sent", HttpStatus.BAD_REQUEST);

        // ACC-1969 ACC-3297 -> REMOVED SENDING EMAIL TO CLIENTS

        // get activation attachments
        String baseURL = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_BACKEND_BASE_URL) + "/public/download/%s?name=%s";

        // get payment receipt attachments
        String receiptAttachmentURL = null;
        if (receiptAttachment != null) {
            String receiptAttachmentName = "Payment Terms Form";
            receiptAttachmentURL = String.format(baseURL, receiptAttachment.getUuid(),
                    receiptAttachmentName.replaceAll("\\s", "_"));
        }
        if (haveToSendMaidVisaAgreementFile) {
            // ACC-2798
            agreementMVContractURL = Setup.getParameter(Setup.getModule("sales"), "maidvisa_website_base_url") +
                    "/app-services/view-mv-agreement?uid=" + contract.getUuid();
        }

        String greetings;
        if (contract.getContractProspectType().getCode().equals(
                PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)))
            greetings = Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA);
        else
            greetings = Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC);

        HashMap<String, String> parameters = new HashMap<>();
        parameters.put("dear_receiver_name", dearClientNameSms);
        parameters.put("client_nickname_or_first_name", StringUtils.getClientNicknameOrFirstName(client));
        parameters.put("greetings", greetings);
        parameters.put("postponed_passed", postponedPassed ? "" : "signed ");
        parameters.put("receipt_attachment_url", receiptAttachmentURL != null ? utils.shorteningUrl(receiptAttachmentURL) : "");
        parameters.put("agreement_MV_contract_url", agreementMVContractURL != null ? utils.shorteningUrl(agreementMVContractURL) : "");

        String msgType = "";
        String templateName = "";
        if (contract.getContractProspectType().getCode()
                .equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {

            msgType = "MV_PAYMENTS_DETAILS";
            if (haveToSendMaidVisaAgreementFile) {
                templateName = receiptAttachment != null ?
                        "MV_PAYMENTS_DETAILS_AGREEMENT_AND_RECEIPT" :
                        "MV_PAYMENTS_DETAILS_AGREEMENT_ONLY";
            } else if (receiptAttachment != null) {
                templateName = "MV_PAYMENTS_DETAILS_RECEIPT_ONLY";
            }
        } else {
            msgType = "CC_PAYMENTS_DETAILS";
            if (receiptAttachment != null) {
                templateName = "CC_PAYMENTS_DETAILS";
            }
        }
        Template t = TemplateUtil.getTemplate(templateName);
        messagingService.sendClientSms(contract, t,
                        parameters,
                        new HashMap<>(),
                        client.getNormalizedMobileNumber(),
                        client.getNormalizedWhatsappNumber(),
                        client.getId(),
                        client.getEntityType());

        if (sendToSpouse) {
            messagingService.sendClientSms(contract, t,
                            parameters,
                            new HashMap<>(),
                            client.getNormalizedSpouseMobileNumber(),
                            client.getNormalizedSpouseWhatsappNumber(),
                            client.getId(),
                            client.getEntityType());
        }

        if (cpt != null) {
            cpt.setReceiptSent(true);
            contractPaymentTermRep.save(cpt);
        }

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    public Map getDirectDebitFiles(
            Contract contract,
            boolean includingInactive) {

        Map termMap = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (termMap == null)  throw new RuntimeException("There is no active Contract Payment Term");

        ContractPaymentTerm activeContractPaymentTerm = (ContractPaymentTerm) termMap.get("contractPaymentTerm");

        if (includingInactive) {
            List<ContractPaymentTerm> notActiveTermsByContract = contractPaymentTermRep.findNotActiveTermsByContract(contract);
            for (ContractPaymentTerm contractPaymentTerm : notActiveTermsByContract) {
                Attachment contract_payments_receipt = contractPaymentTerm.getAttachment("contract_payments_receipt");
                if (contract_payments_receipt != null) {
                    List<Attachment> attachments = activeContractPaymentTerm.getAttachments();
                    attachments.add(contract_payments_receipt);
                    activeContractPaymentTerm.setAttachments(attachments);
                }
                Attachment contract_payments_receipt_old = contractPaymentTerm.getAttachment("contract_payments_receipt_old");
                if (contract_payments_receipt_old != null) {
                    List<Attachment> attachments = activeContractPaymentTerm.getAttachments();
                    attachments.add(contract_payments_receipt_old);
                    activeContractPaymentTerm.setAttachments(attachments);
                }
            }
        }

        return contractPaymentTermHelper.projectResultMap(
                getDirectDebitFilesOfContractPaymentTerm(activeContractPaymentTerm));
    }

    @Transactional
    public DirectDebit addNewDD(
            Contract contract, Date fromDate, Date toDate,
            Double additionalDiscount, String notes,
            Long attachmentId, Double amount, Double suggestedAmount,
            DirectDebitType directDebitType, PicklistItem oneTimePaymentType,
            boolean useOldSignatures, Payment replaceOfPayment,
            boolean finalAmount, boolean withIncomplete, boolean saveChanges,
            ContractPaymentTerm contractPaymentTerm,
            boolean checkConcurrentModification) {

        return addNewDD(
                contract, fromDate, toDate, additionalDiscount, notes,
                attachmentId, amount, suggestedAmount, directDebitType, oneTimePaymentType,
                useOldSignatures, replaceOfPayment, finalAmount, withIncomplete, saveChanges,
                contractPaymentTerm, checkConcurrentModification, new HashMap<>());
    }

    // finalAmount flag required in ACC-2592
    @Transactional
    public DirectDebit addNewDD(
            Contract contract, Date fromDate, Date toDate,
            Double additionalDiscount, String notes,
            Long attachmentId, Double amount, Double suggestedAmount,
            DirectDebitType directDebitType, PicklistItem oneTimePaymentType,
            boolean useOldSignatures, Payment replaceOfPayment,
            boolean finalAmount, boolean withIncomplete, boolean saveChanges,
            ContractPaymentTerm contractPaymentTerm,
            boolean checkConcurrentModification,
            Map<String, Object> map) {

        String checkIfLockedKey = ContractPaymentTermController.class.getSimpleName() + "addNewDD" + Contract.class.getSimpleName();
        Long contractId = contract.getId();

        if (checkConcurrentModification) {
            ConcurrentModificationHelper.lockOrThrowException(checkIfLockedKey, contractId);
        }

        DirectDebit directDebit;

        try {
            logger.info("directDebitType: " + directDebitType +
                    "; oneTimePaymentType: " + oneTimePaymentType +
                    "; suggestedAmount: " + suggestedAmount +
                    "; \nfromDate: " + fromDate +
                    "; toDate: " + toDate +
                    "; additionalDiscount: " + additionalDiscount +
                    "; amount: " + amount);

            if (replaceOfPayment != null) {
                logger.info("MMM it's replacement");

                if (!directDebitType.equals(DirectDebitType.ONE_TIME)) {
                    throw new BusinessException("The payment must be on ONE TIME Direct Debit only");
                }
                if (replaceOfPayment.getReplaced()) {
                    throw new BusinessException("The payment has already been replaced");
                }
                if (contractPaymentRep.existsOldReplaceOfPayment(replaceOfPayment, new ArrayList<DirectDebitStatus>() {{
                    add(DirectDebitStatus.REJECTED);
                    add(DirectDebitStatus.CANCELED);
                }})) {
                    throw new BusinessException("The payment has already been replaced with DD payment");
                }
            }
            if (contractPaymentTerm == null) {
                contractPaymentTerm = contract.getActiveContractPaymentTerm();
            }
            logger.info("MMM active CPT: " + contractPaymentTerm.getId());

            //ACC-5056
            if (directDebitType.equals(DirectDebitType.MONTHLY)) {
                AccountingEntityProperty property = accountingEntityPropertyRepository
                        .findByOriginAndKeyAndDeletedFalse(contract.getId(), Contract.OEC_AMEND_DDS);
                if (property == null || !DateUtil.formatDateDashed(new Date())
                        .equals(DateUtil.formatDateDashed(property.getCreationDate()))) {

                    if (directDebitRepository.getOverlappingDD(contractPaymentTerm, fromDate, toDate) ||
                            directDebitRepository.getOverlappingInCompleteDDs(contractPaymentTerm, fromDate, toDate)) {
                        throw new BusinessException("The new DDs period or part of it is covered by another DD");
                    }
                }
            }

            DateTime convertedStartDate = new DateTime(fromDate).withTimeAtStartOfDay();
            DateTime convertedEndDate = directDebitType.equals(DirectDebitType.MONTHLY) ?
                    new DateTime(toDate).withTimeAtStartOfDay() :
                    convertedStartDate;

            Attachment attachment = Storage.getAttchment(attachmentId);

            if (!directDebitType.equals(DirectDebitType.ONE_TIME)) {
                convertedStartDate = convertedStartDate.withDayOfMonth(1);
                convertedEndDate = convertedEndDate.withDayOfMonth(1);
            }

            if(oneTimePaymentType != null) {
                oneTimePaymentType = picklistItemRepository.findOne(oneTimePaymentType.getId());
            }

            if (!finalAmount && contractPaymentTermHelper.shouldApplyDiscount(
                    contractPaymentTerm, amount, fromDate, directDebitType, oneTimePaymentType)) {

                amount += contractPaymentTerm.getDiscount();
            }

            if (additionalDiscount != null) {
                int duration = 0;
                DateTime additionalDiscountEndDate = new DateTime(calculateDiscountsWithVatService.getAdditionalDiscountEndDateInMillis(contractPaymentTerm)).minusMonths(1)
                        .withDayOfMonth(1);
                if (additionalDiscountEndDate.isBefore(convertedEndDate)) {
                    duration = Months.monthsBetween(convertedStartDate, additionalDiscountEndDate).getMonths() + 1;
                } else {
                    duration = Months.monthsBetween(convertedStartDate, convertedEndDate).getMonths() + 1;
                }
                calculateDiscountsWithVatService.validateAdditionalDiscount(additionalDiscount, duration,
                        contractPaymentTerm.getPaymentTermConfig().getMonthlyPayment());
            }

            logger.info("start generating payments: " +
                    "convertedStartDate : " + convertedStartDate+
                    "; convertedEndDate: " + convertedEndDate +
                    "; additionalDiscount: " + additionalDiscount +
                    "; amount: " + amount +
                    "; directDebitType: " + directDebitType +
                    "; oneTimePaymentType: " + oneTimePaymentType);

            List<ContractPayment> payments = generateDDPaymentsBetweenDates(convertedStartDate, convertedEndDate,
                    additionalDiscount, null, contractPaymentTerm, amount,
                    directDebitType, oneTimePaymentType, finalAmount, map);

            if (replaceOfPayment != null && directDebitType.equals(DirectDebitType.ONE_TIME)) {
                payments.forEach(contractPayment -> {
                    contractPayment.setReplaceOf(replaceOfPayment);
                });
            }
            ContractPayment firstDiscountedPayment = payments.stream()
                    .filter(contractPayment -> contractPayment.getAdditionalDiscountAmount() != null
                            && contractPayment.getAdditionalDiscountAmount() > 0)
                    .findFirst().orElse(null);
            if (((additionalDiscount != null && additionalDiscount > 0) || notes != null || attachmentId != null) && firstDiscountedPayment == null) {
                throw new BusinessException("There is no possible debit debit to add discount on it");
            }
            Map<String, Object> signatureType = directDebitSignatureService.getLastSignatureType(contractPaymentTerm, false, false);

            useOldSignatures = useOldSignatures && ((Boolean) signatureType.get("useApprovedSignature")
                    || (Boolean) signatureType.get("useNonRejectedSignature"));

            // ACC-7093
            if (map != null && !map.isEmpty() && additionalDiscount == null) {
                for (ContractPayment p : payments) {
                    // Add Prorated
                    if (directDebitType.equals(DirectDebitType.ONE_TIME) &&
                            map.containsKey("isProRated") && (Boolean) map.get("isProRated")) {
                        p.setIsProRated(true);
                    }

                    // Add Discount
                    if (map.get("additionAmount") != null && (Double) map.get("additionAmount") != 0.0) {
                        p.setAdditionAmount((Double) map.get("additionAmount"));
                    } else if ((map.get("additionalDiscountAmount") != null && (Double) map.get("additionalDiscountAmount") != 0.0) ||
                            (map.get("moreAdditionalDiscount") != null && (Double) map.get("moreAdditionalDiscount") != 0.0)) {

                        if (map.get("additionalDiscountAmount") != null && (Double) map.get("additionalDiscountAmount") != 0.0) {
                            p.setAdditionalDiscountAmount((Double) map.get("additionalDiscountAmount"));
                        }
                        if (map.get("moreAdditionalDiscount") != null && (Double) map.get("moreAdditionalDiscount") != 0.0) {
                            p.setMoreAdditionalDiscount((Double) map.get("moreAdditionalDiscount"));
                        }
                    } else if (directDebitType.equals(DirectDebitType.MONTHLY) &&
                            p.getAdditionalDiscountAmount() == 0.0 && p.getMoreAdditionalDiscount() == 0.0) {
                        Map<String, Object> m = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(contractPaymentTerm, new LocalDate(p.getDate()));

                        if (m.get("additionalDiscountAmountPerPayment") != null && (Double) m.get("additionalDiscountAmountPerPayment") != 0.0) {
                            p.setAdditionalDiscountAmount((Double) m.get("additionalDiscountAmountPerPayment"));
                        }
                        if (m.get("moreAdditionalDiscount") != null && (Double) m.get("moreAdditionalDiscount") != 0.0) {
                            p.setMoreAdditionalDiscount((Double) m.get("moreAdditionalDiscount"));
                        }
                    }
                }
            }

            // ACC-2992
            saveChanges = saveChanges && (!directDebitType.equals(DirectDebitType.ONE_TIME) || oneTimePaymentType != null);
            updateContractPaymentTermWithPayments(contractPaymentTerm, true, useOldSignatures,
                    payments, null,
                    false, true, saveChanges,
                    false, false, withIncomplete, false, map);

            if (payments.isEmpty()) throw new BusinessException("No direct debit was generated");

            directDebit = payments.get(0).getDirectDebit();
            logger.info("first DD id: " + directDebit.getId());

            if (map.containsKey("relatedEntityId") && map.containsKey("relatedEntityType")) {
                directDebit.setRelatedEntityId((Long) map.get("relatedEntityId"));
                directDebit.setRelatedEntityType((String) map.get("relatedEntityType"));
            }

            // ACC-7149
            if (!directDebitType.equals(DirectDebitType.MONTHLY)) {
                for (ContractPayment payment : payments) {
                    logger.info("payment id: " + payment.getId() +
                            "; amount: " + payment.getAmount() +
                            "; date: " + payment.getDate() +
                            "; one time: " + payment.isOneTime() +
                            ";\n method: " + payment.getPaymentMethod() +
                            "; dd id: " + payment.getDirectDebit().getId() +
                            "; prorated: " + payment.getIsProRated() +
                            "; payment type: " + payment.getPaymentType());

                    if (payment.getDirectDebit().getId() != null && directDebit.getId() != null &&
                            !payment.getDirectDebit().getId().equals(directDebit.getId()))
                        throw new BusinessException("More than one direct debit were generated");
                }
            }

            if (firstDiscountedPayment != null) {
                directDebit = firstDiscountedPayment.getDirectDebit();
                directDebit.setAdditionalDiscountNotes(notes);
                if (attachment != null) {
                    directDebit.addAttachment(attachment);
                }
            }
            // directDebit.setNonCompletedInfo(true);
            directDebit.setSuggestedAmount(suggestedAmount);
            directDebitRepository.save(directDebit);

            ConcurrentModificationHelper.unLock(checkIfLockedKey, contractId);
        } catch (BusinessException e) {
            logger.severe("BusinessException while adding new DD to Contract#" + contractId);
            ConcurrentModificationHelper.unLock(checkIfLockedKey, contractId);
            throw e;
        } catch (Exception e) {
            logger.severe("Exception while adding new DD to Contract#" + contractId);
            ConcurrentModificationHelper.unLock(checkIfLockedKey, contractId);
            throw new RuntimeException(e);
        }

        return directDebit;
    }

    public List<ContractPayment> generateDDPaymentsBetweenDates(
            DateTime startDate, DateTime endDate,
            Double additionalDiscountAmount, Integer fixedNumOfAdditionalDiscountPayment,
            ContractPaymentTerm contractPaymentTerm,
            Double amount) {

        return generateDDPaymentsBetweenDates(startDate, endDate,
                additionalDiscountAmount, fixedNumOfAdditionalDiscountPayment,
                contractPaymentTerm, amount,
                DirectDebitType.MONTHLY, null,
                false, new HashMap<>());
    }

    public List<ContractPayment> generateDDPaymentsBetweenDates(
            DateTime startDate, DateTime endDate,
            Double additionalDiscountAmount, Integer fixedNumOfAdditionalDiscountPayment,
            ContractPaymentTerm contractPaymentTerm,
            Double amount, DirectDebitType directDebitType, PicklistItem oneTimePaymentType,
            boolean finalAmount, Map<String, Object> map) {

        PicklistItem subType = map.containsKey("subType") ? (PicklistItem) map.get("subType") : null;
        boolean addedManuallyFromClientProfile = map.containsKey("addedManuallyFromClientProfile") && (boolean) map.getOrDefault("addedManuallyFromClientProfile", false);

        logger.info("generateDDPaymentsBetweenDates, startDate: " + new LocalDate(startDate.toDate().getTime()).toString("yyyy-MM-dd") +
                "; endDate: " + new LocalDate(endDate.toDate().getTime()).toString("yyyy-MM-dd") +
                "; additionalDiscountAmount: " + additionalDiscountAmount +
                "; fixedNumOfAdditionalDiscountPayment: " + fixedNumOfAdditionalDiscountPayment +
                "; amount: " + amount + "; directDebitType: " + directDebitType);

        PicklistItem paymentType = oneTimePaymentType != null && directDebitType.equals(DirectDebitType.ONE_TIME)?
                oneTimePaymentType :
                contractPaymentTermHelper.getItem("TypeOfPayment", "monthly_payment");

        long discountStartDateInMillis = calculateDiscountsWithVatService.getDiscountStartDateInMillis(contractPaymentTerm);
        logger.info("DiscountStartDateInMillis: " + discountStartDateInMillis);

        List<ContractPayment> payments = new ArrayList();
        int paymentsCount = 0;
        for (DateTime date = startDate; date.isBefore(endDate) ||
                date.isEqual(endDate); date = date.plusMonths(1).withDayOfMonth(1)) {

            AbstractPaymentTypeConfig contractPaymentType =
                    contractPaymentTerm.getPaymentTypeConfig(paymentType.getCode());

            ContractPayment payment = new ContractPayment();
            payment.setContractPaymentTerm(contractPaymentTerm);
            payment.setPaymentType(paymentType);
            payment.setSubType(subType);
            payment.setDescription("");
            payment.setDate(date.toDate());

            payment.setAddedManuallyFromClientProfile(addedManuallyFromClientProfile);

            if (contractPaymentType != null) {
                if (subType == null) {
                    payment.setSubType(contractPaymentType.getSubType());
                }
                payment.setDescription(contractPaymentType.getDescription());
                payment.setAffectsPaidEndDate(contractPaymentType.getAffectsPaidEndDate());
            }

            payment.setAmount(amount != null ? amount : contractPaymentTerm.getMonthlyPayment());
            calculateDiscountsWithVatService.updatePaymentAmountWithDiscountAndAddonAndDetails(
                    contractPaymentTerm, payment, discountStartDateInMillis,
                    finalAmount);

            payment.setDescriptionForSigningScreen(paymentType.getCode().equals("same_day_recruitment_fee") ?
                    ContractPaymentTermController.PAYMENT_DESCRIPTION_AGENCY_FEE :
                    contractPaymentTermHelper.getPaymentDescriptionForSigningScreen(payment)); // ACC-7151
            payment.setPaymentMethod(PaymentMethod.DIRECT_DEBIT);
            payment.setOneTime(directDebitType.equals(DirectDebitType.ONE_TIME));
            payment.setIsCalculated(true);

            payments.add(payment);

            paymentsCount++;
        }

        if (additionalDiscountAmount != null && additionalDiscountAmount > 0) {
            if (fixedNumOfAdditionalDiscountPayment != null) {
                paymentsCount = fixedNumOfAdditionalDiscountPayment;
            }

            // ACC-3272
            calculateDiscountsWithVatService.updatePaymentsAdditionalDiscountAndAmount(
                    paymentsCount, additionalDiscountAmount, payments, 0.0);
        }

        return payments;
    }

    public Map getDirectDebitFilesOfContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        Map result = new HashMap();
        List<DirectDebit> directDebits = directDebitRepository.findByContractPaymentTerm(contractPaymentTerm);
        List<Attachment> Attachments = contractPaymentTerm.getAttachments();
        result.put("contractPaymentTerm", contractPaymentTerm);
        result.put("directDebits", directDebits);
        result.put("paymentsReceiptAttachment",
                Attachments.stream().filter(attachment -> attachment.getTag().startsWith(FILE_TAG_PAYMENTS_RECEIPT))
                        .findFirst().orElse(null));
        return result;
    }

    public void generateAndSavePaymentsReceipt(
            ContractPaymentTerm cpt,
            Client client, Housemaid housemaid,
            InputStream signature,
            Boolean attachmentCompletionCheckedBefore) {

        if (cpt.getAttachments().stream().anyMatch(a -> a.getTag().equals(FILE_TAG_PAYMENTS_RECEIPT)) ||
                (!attachmentCompletionCheckedBefore &&
                        !contractPaymentTermHelper.isAttachmentInfoCompleted(cpt.getContract()))) return;

//        if (signature == null) {
//            logger.log(Level.SEVERE, "generateAndSavePaymentsReceipt return because signatures is null");
//            return;
//        }

        Attachment paymentsReceipt = PaymentReceiptHelper.generatePaymentsReceipt(
                cpt, client, housemaid, signature, cpt.getPaymentTermsTemplate());
        if(paymentsReceipt == null) return;

        cpt = contractPaymentTermRep.findOne(cpt.getId());
        cpt.addAttachment(paymentsReceipt);

        contractPaymentTermRep.save(cpt);

    }

    public Map<?, ?> getContractPaymentTermByContractWithDirectDebitPayments(
            ContractPaymentTerm contractPaymentTerm) {

        Map result = new HashMap<>();
        List<ContractPayment> payments = new ArrayList<>();

        if (!contractPaymentTerm.isForceGenerateDds()) {
            payments = contractPaymentRep.findByContractPaymentTermAndAndDirectDebitStatusNotIn(
                    contractPaymentTerm,
                    Arrays.asList(
                            DirectDebitStatus.CANCELED,
                            DirectDebitStatus.PENDING_FOR_CANCELLATION,
                            DirectDebitStatus.EXPIRED));
        }

        // PAYMENTS
        Map<String, Object> paymentsMap = getDefaultDirectDebitPayments(contractPaymentTerm, new HashMap<>());
        payments.addAll((List<ContractPayment>)paymentsMap.getOrDefault("payments", new ArrayList<>()));
        result.put("payments", payments);

        // DIRECT DEBITS
        List<DirectDebit> directDebits = directDebitService.getDirectDebitsOfPayments(
                payments, contractPaymentTerm, false);
        directDebits.addAll(directDebitRepository.getWithoutPaymentsDD(contractPaymentTerm));
        result.put("directDebits", directDebits);

        result.put("clientHasFrontEIDDocument", contractPaymentTermHelper.hasClientFrontSideDoc((Client) result.get("client")));

        return result;
    }

    public Map<String, Object> getDefaultDirectDebitPayments(ContractPaymentTerm contractPaymentTerm, Map<String, Object> m) {
        // ACC-5252
        if (Setup.getApplicationContext().getBean(DirectDebitService.class)
                .hasActiveDD(contractPaymentTerm.getContract()) &&
                !contractPaymentTerm.isForceGenerateDds())
            return new HashMap<>();

        Contract contract = contractRep.findOne(contractPaymentTerm.getContract().getId());

        Map<String, Object> nonExistingDefaultPayments = getNonExistingDefaultPayments(contractPaymentTerm, m);

        List<ContractPayment> initialPayments = (List<ContractPayment>)nonExistingDefaultPayments.getOrDefault("defaultPayments", new ArrayList<>());
        Long alreadyAdditionalDiscountPayments = (Long) nonExistingDefaultPayments.getOrDefault("alreadyAdditionalDiscountPayments", 0L);
        Double alreadyAdditionalDiscountPaymentsAmount = (Double) nonExistingDefaultPayments.getOrDefault("alreadyAdditionalDiscountPaymentsAmount", 0D);

        List<ContractPayment> payments = new ArrayList<>();
        payments.addAll(initialPayments);

        // ACC-8662
        if (Setup.getApplicationContext()
                .getBean(ContractService.class)
                .hasPreventCreateOtherDds(contract)) {

            Map<String, Object> output = new HashMap<>();
            output.put("payments", payments.stream()
                    .filter(cp -> cp.getPaymentType().hasTag(AbstractPaymentTypeConfig.PREVENT_OTHER_DDS_TAG))
                    .collect(Collectors.toList()));
            return output;
        }

        DateTime contractStartDate = new DateTime(contract.getStartOfContract());
        if (contractPaymentTerm.getProRatedDays() > 0) {
            contractStartDate.plusDays(contractPaymentTerm.getProRatedDays());
        }

        int paymentsDuration = contract.getPaymentsDuration() +
                contractPaymentTermExtendRepository.getExtendDurationByContractTerm(
                        contractPaymentTerm);
        logger.log(Level.SEVERE, "paymentsDuration " + paymentsDuration);

        // ACC-3225
        AbstractPaymentTypeConfig monthlyContractPaymentType = contractPaymentTerm.getPaymentTypeConfig(
                AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        if (paymentsDuration > 0 && monthlyContractPaymentType != null) {

            List<ContractPayment> monthlyDda = payments.stream()
                    .filter(cp -> cp.getPaymentType().getCode().equals("monthly_payment"))
                    .collect(Collectors.toList());

            DateTime lastDdaStartDate = monthlyDda.isEmpty() ?
                    contractStartDate.plusMonths(monthlyContractPaymentType.getStartsOn()) :
                    new DateTime(monthlyDda.stream().map(ContractPayment::getDate)
                            .max(Date::compareTo).get());
            DateTime ddbStartDate = lastDdaStartDate.plusMonths(1);
            DateTime lastPaymentReceived = Setup.getApplicationContext().getBean(ContractPaymentService.class)
                    .getLastMonthlyPaymentDate(contract);

            if (lastPaymentReceived != null && lastPaymentReceived.isAfter(lastDdaStartDate)) {
                ddbStartDate = lastPaymentReceived.plusMonths(1);
            }
            logger.log(Level.INFO, "lastDdaStartDate: {0}; ddbStartDate: {1}; lastPaymentReceived: {2}",
                    new Object[]{lastDdaStartDate, ddbStartDate, lastPaymentReceived});
            int paymentsCount = Months.monthsBetween(contractStartDate.withTimeAtStartOfDay(),
                    ddbStartDate.dayOfMonth().withMaximumValue()).getMonths();
            logger.info("Monthly payments count: " + paymentsCount);

            int dayOfMonth = contractStartDate.getDayOfMonth();
            logger.info("dayOfMonth " + dayOfMonth);


            // ACC-2954
            Integer numOfAdditionalDiscountPayment = contractPaymentTerm.getAdditionalDiscountMonthsCount(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) != null ?
                    contractPaymentTerm.getAdditionalDiscountMonthsCount(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) - alreadyAdditionalDiscountPayments.intValue()
                    : 0;

            Double additionalDiscountAmount = contractPaymentTerm.getAdditionalDiscount() != null ?
                    contractPaymentTerm.getAdditionalDiscount() - alreadyAdditionalDiscountPaymentsAmount
                    : 0D;

            logger.info("numOfAdditionalDiscountPayment: " + numOfAdditionalDiscountPayment +
                    "; additionalDiscountAmount: " + additionalDiscountAmount +
                    "; alreadyAdditionalDiscountPayments: " + alreadyAdditionalDiscountPayments);

            // ACC-2077, ACC-2362 -> consider MV Contract in second FULL Payment by DDA
            if (!contract.isOneMonthAgreement() //ACC-4905
                    && paymentsCount < 2 && (contract.isMaidVisa() ||
                    (contract.isMaidCc() &&
                            ((contract.getIsProRated() && !contract.getProRatedPlusMonth()) ||
                                    (!contract.getIsProRated() && // ACC-2194
                                            dayOfMonth > Integer.parseInt(Setup.getParameter(
                                                    Setup.getCurrentModule(),
                                                    AccountingModule.PARAMETER_NOT_PRORATED_CONTRACT_START_DATE))))))) {

                // ACC-2362
                int shift = contract.isMaidVisa() &&
                        (contract.getProRatedPlusMonth() ||
                        contractStartDate.getDayOfMonth() == contractStartDate.dayOfMonth()
                                .withMaximumValue().getDayOfMonth()) ? 2 : 1;

                if (shift >= monthlyContractPaymentType.getStartsOn()) {
                    logger.info("shift >= monthlyContractPaymentType.getStartsOn() ONE TIME");

                    DateTime paymentDate = contractStartDate.plusMonths(shift).withDayOfMonth(1);

                    ContractPayment payment = contractPaymentTermHelper.createMonthlyContractPayment(
                            contractPaymentTerm, paymentDate.toDate());
                    payment.setOneTime(true);
                    payment.setPaymentMethod(PaymentMethod.DIRECT_DEBIT);
                    logger.info("paymentDate " + paymentDate);

                    if (calculateDiscountsWithVatService.updatePaymentsAdditionalDiscountAndAmount(
                            numOfAdditionalDiscountPayment, additionalDiscountAmount,
                            Arrays.asList(new ContractPayment[]{payment}), 0D)) {

                        numOfAdditionalDiscountPayment--;
                        additionalDiscountAmount -= payment.getAdditionalDiscountAmount();
                    }

                    payment.setDescriptionForSigningScreen(contractPaymentTermHelper.getPaymentDescriptionForSigningScreen(payment));
                    payments.add(calculateDiscountsWithVatService.updatePaymentAmountWithDiscountAndAddonAndDetails(contractPaymentTerm, payment, false));
                    ddbStartDate = paymentDate.plusMonths(1);
                }
            }

            // ACC-3225
            if (monthlyContractPaymentType.getStartsOn() > 0) {
                logger.info("DdGenerationNextMonth true");

                contract.setDdGenerationNextMonth(true);
                contractRep.save(contract);
            }

            if (contract.getProRatedPlusMonth() &&
                    ddbStartDate.equals(contractStartDate.plusMonths(monthlyContractPaymentType.getStartsOn() + 1))) {

                ddbStartDate = ddbStartDate.plusMonths(1);
            }

            logger.log(Level.INFO, "lastDdaStartDate: {0}; ddbStartDate: {1}; lastPaymentReceived: {2}",
                    new Object[]{lastDdaStartDate, ddbStartDate, lastPaymentReceived});

            DateTime directDebitEndDate = contractStartDate.plusMonths(
                    contractPaymentTerm.isIsProRated() ?
                            paymentsDuration : paymentsDuration - 1)
                    .withDayOfMonth(1);

            payments.addAll(generateDDPaymentsBetweenDates(
                    ddbStartDate.withDayOfMonth(1), directDebitEndDate,
                    additionalDiscountAmount, numOfAdditionalDiscountPayment,
                    contractPaymentTerm, null));

            // ACC-4905 #4 set second dd as one time
            if (contract.isOneMonthAgreement() && initialPayments.size() == 1 && payments.size() > 1) {
                ContractPayment contractPayment = payments.get(1);
                contractPayment.setOneTime(true);
            }
        }

        // ACC-5252
        if (!payments.isEmpty() &&
                !payments.get(0).isOneTime() &&
                Setup.getApplicationContext().getBean(AfterCashFlowService.class)
                        .existsRunningFlow(contractPaymentTerm))
            payments.get(0).setOneTime(true);

        Map<String, Object> output = new HashMap<>();
        output.put("payments", payments);

        return output;
    }

    private void fillDiscountMap(List<ContractPayment> payments, Map<String, Double> discountsMap) {
        payments.stream()
                .filter(p -> p.getPaymentType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) &&
                        p.getAdditionalDiscountAmount() != null && p.getAdditionalDiscountAmount() > 0D)
                .forEach(p -> discountsMap.put(new LocalDate(p.getDate().getTime()).toString("yyyy-MM"), p.getAdditionalDiscountAmount()));
    }

    public Map<String, Object> getNonExistingDefaultPayments(ContractPaymentTerm cpt, Map<String, Object> m) {
        Map<String, Double> discountsMap = new HashMap<>();
        Map<String, Object> initialMap = getDefaultInitialPayments(cpt, m);

        fillDiscountMap(contractPaymentRep.findByContractPaymentTerm_Contract(cpt.getContract()), discountsMap);

        List<LocalDate> startDates = discountsMap.keySet().stream().map(s -> LocalDate.parse(s + "-01")).collect(Collectors.toList());
        startDates.forEach(d -> {
            if(paymentRepository.existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                    cpt.getContract(), PaymentStatus.RECEIVED, "monthly_payment",
                    d.toDate(), d.withDayOfMonth(d.dayOfMonth().withMaximumValue().getDayOfMonth()).toDate())) return;

            discountsMap.remove(d.toString("yyyy-MM"));
        });

        List<ContractPayment> defaultPayments = (List<ContractPayment>)initialMap.get("payments");

        List<ContractPayment> cashPayments = contractPaymentRep.findNonDDContractPayment(
                cpt.getContract(), null);

        Set<String> existingPayments = cashPayments.stream()
                .map(cp -> cp.getPaymentType().getCode() + "_" + new LocalDate(cp.getDate()).toString("yyyy-MM"))
                .collect(Collectors.toSet());

        defaultPayments = defaultPayments.stream()
                .filter(cp -> {
                    String key = cp.getPaymentType().getCode() + "_" + new LocalDate(cp.getDate()).toString("yyyy-MM");
                    boolean alreadyExists = existingPayments.contains(key);

                    if(alreadyExists) {
                        logger.info("Payment ' " + key + " ' already exists by cash");
                    }
                    return !alreadyExists;
                })
                .collect(Collectors.toList());

        fillDiscountMap(defaultPayments, discountsMap);

        //cause if cash they should have been created before
        long alreadyAdditionalDiscountPayments = discountsMap.keySet().size();
        double alreadyAdditionalDiscountPaymentsAmount = discountsMap.values().stream().reduce(0D, Double::sum);

        logger.info("alreadyAdditionalDiscountPaymentsAmount: " + alreadyAdditionalDiscountPaymentsAmount +
                "; alreadyAdditionalDiscountPayments: " + alreadyAdditionalDiscountPayments);

        Map<String, Object> output = new HashMap<>();
        output.put("defaultPayments", defaultPayments);
        output.put("alreadyAdditionalDiscountPayments", alreadyAdditionalDiscountPayments);
        output.put("alreadyAdditionalDiscountPaymentsAmount", alreadyAdditionalDiscountPaymentsAmount);

        return output;
    }

    // Returns a list of 1) non monthly payments 2) one time monthly payments
    public Map<String, Object> getDefaultInitialPayments(ContractPaymentTerm contractPaymentTerm, Map<String, Object> m) {
        Map<String, Object> output = new HashMap<>();
        List<ContractPayment> payments = new ArrayList();

        Contract contract = contractRep.findOne(contractPaymentTerm.getContract().getId());
        DateTime contractStartDate = new DateTime(contract.getStartOfContract());
        Integer paymentsDuration = contract.getPaymentsDuration();
        int directDebitCount = contract.getIsProRated() ? paymentsDuration : paymentsDuration - 1;

        DateTime contractEndDate = contractStartDate.plusMonths(directDebitCount).withDayOfMonth(1);

        List<ContractPaymentType> notMonthlyContractPaymentTypes = contractPaymentTerm.getContractPaymentTypes() != null ?
                contractPaymentTerm.getContractPaymentTypes().stream()
                        .filter(contractPaymentType ->
                                (!contractPaymentType.isPostponedDdGenerated() || (boolean) m.getOrDefault("ignorePostponedDdGenerated", false)) &&
                                !Arrays.asList(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                                                AbstractPaymentTypeConfig.MONTHLY_PAYMENT_ADD_ON_TYPE_CODE)
                                        .contains(contractPaymentType.getType().getCode()))
                        .collect(Collectors.toList()) :
                new ArrayList<>();

        for (ContractPaymentType contractPaymentType : notMonthlyContractPaymentTypes) {
            logger.log(Level.INFO, "Generating Payment of type: {0}", contractPaymentType.getType().getCode());
            if (contractPaymentType.getRecurrence() > 0) {
                Map<String, Object> plansMap = Setup.getApplicationContext()
                        .getBean(DirectDebitGenerationPlanService.class)
                        .buildDirectDebitGenerationPlans(
                                contractPaymentType, contractStartDate.withTimeAtStartOfDay(),
                                contractEndDate.withTimeAtStartOfDay(), true);

                if (plansMap.containsKey("contractPayment")) {
                    payments.add((ContractPayment) plansMap.get("contractPayment"));
                }
            } else {
                List<ContractPayment> contractPayments = contractPaymentType.generateContractPayments(contractStartDate.withTimeAtStartOfDay(), contractEndDate.withTimeAtStartOfDay());
                payments.addAll(contractPayments);

                // ACC-9222
                // Update the 'PostponedDdGenerated' flag in ContractPaymentType after inserting a ContractPayment, if applicable
                if (!contractPaymentType.isPostponedDdGenerated()) {
                    contractPayments.forEach(cp -> cp.setContractPaymentTypeId(contractPaymentType.getId()));
                }
            }
        }

        AbstractPaymentTypeConfig monthlyContractPaymentType = contractPaymentTerm.getPaymentTypeConfig(
                AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        if(monthlyContractPaymentType != null) {
            // ACC-2077
            switch (contract.getContractProspectType().getCode()) {
                case "maids.cc_prospect":
                    logger.info("CC");
                    // ACC-4905
                    if (contract.isOneMonthAgreement()) {
                        payments.addAll(contractPaymentTermHelper
                            .createOneMonthAgreementAndProratedPayment(contractPaymentTerm, contractStartDate));
                        break;
                    }
                    // ACC-1590
                    if (monthlyContractPaymentType.getStartsOn().equals(0) &&
                            contract.getIsProRated() &&
                            contractPaymentTerm.getFirstMonthPayment() > 0 &&
                            !contract.getProRatedPlusMonth()) {

                        logger.info("Prorated");
                        // prorated payment
                        payments.add(contractPaymentTermHelper.createProratedPayment(
                                contractPaymentTerm, contractStartDate));
                    }

                    // if cash -> two separated payments one prorated and one for first full month, if not cash -> one DDA for prorated + first full payment
                    if (monthlyContractPaymentType.getStartsOn() <= 1 &&
                            contract.getProRatedPlusMonth()) {
                        logger.info("Prorated Plus One Month");
                        ContractPayment proPlusMonthPayment = contractPaymentTermHelper.createProratedPlusMonthPayment(
                                contractPaymentTerm, contractStartDate);

                        payments.add(proPlusMonthPayment);
                    }

                    // if not prorated
                    if (!contract.getIsProRated() &&
                            monthlyContractPaymentType.getStartsOn().equals(0)) {

                        logger.info("Not Prorated");
                        // first full payment
                        payments.add(contractPaymentTermHelper.createMonthlyContractPayment(
                                contractPaymentTerm, contractStartDate.toDate()));
                        //both in one DD
                    }
                    break;

                case "maidvisa.ae_prospect":
                    int dayOfMonth = contractStartDate.getDayOfMonth();
                    logger.log(Level.SEVERE, "MV dayOfMonth: " + dayOfMonth);

                    boolean isFirstFullPayment = monthlyContractPaymentType.getStartsOn().equals(0) &&
                            dayOfMonth < Integer.parseInt(Setup.getParameter(
                                    Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_MAID_VISA_CONTRACT_START_DATE_FOR_INITIAL_PAYMENTS
                            ));

                    if (isFirstFullPayment) {
                        // First full payment
                        payments.add(contractPaymentTermHelper.createMonthlyContractPayment(
                                contractPaymentTerm, contractStartDate.toDate()));

                        if (ContractService.isPreCollectedSalary(contract)) {
                            contractPaymentTermHelper.createPaymentsForPreCollectedSalaryContract(
                                    contractPaymentTerm, contractStartDate, payments);
                        }
                        break;
                    }

                    if (dayOfMonth < contractStartDate.dayOfMonth().withMaximumValue().getDayOfMonth()) {
                        contractPaymentTermHelper.handleProratedPayment(contract, contractPaymentTerm, contractStartDate, payments, monthlyContractPaymentType);
                    } else {
                        contractPaymentTermHelper.handleFirstFullPayment(contract, contractPaymentTerm, contractStartDate, payments, monthlyContractPaymentType);
                    }
                    break;
            }

            calculateDiscountsWithVatService.updatePaymentsAdditionalDiscountAndAmount(
                contractPaymentTerm.getAdditionalDiscountMonthsCount(
                        AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE),
                contractPaymentTerm.getAdditionalDiscount(), payments,
                0D);
        }

        payments.forEach(payment -> {
            payment.setIsCalculated(true);
            payment.setOneTime(true);
            payment.setPaymentMethod(PaymentMethod.DIRECT_DEBIT);
            payment.setDescriptionForSigningScreen(contractPaymentTermHelper.getPaymentDescriptionForSigningScreen(payment));
        });

        // ACC-4905
        if (contract.isOneMonthAgreement()) {
            boolean hasCreditNote = contractPaymentTerm.getCreditNote() != null && contractPaymentTerm.getCreditNote() > 0 &&
                    contractPaymentTerm.getCreditNoteMonths() != null && contractPaymentTerm.getCreditNoteMonths() > 0;

            // ACC-7151
            output.put("payments", hasCreditNote ?
                    payments.stream()
                            .map(payment -> {
                                calculateDiscountsWithVatService.updateAmountContractPaymentTermDetailsViaContractPayment(
                                        contractPaymentTerm, payment);
                                return payment;})
                            .collect(Collectors.toList()) :
                    payments);

            return output;
        }

        long discountStartDateInMillis = calculateDiscountsWithVatService.getDiscountStartDateInMillis(contractPaymentTerm);
        output.put("payments", payments.stream()
                .map(payment -> {

                    // ACC-7151
                    // the normal discount and add-on not apply on non-monthly DDs
                    // updateAmountContractPaymentTermDetailsViaContractPayment already Applied
                    if (PaymentHelper.isMonthlyPayment(payment.getPaymentType())) {
                        calculateDiscountsWithVatService.updatePaymentAmountWithDiscountAndAddonAndDetails(
                                contractPaymentTerm, payment, discountStartDateInMillis, false);
                    }
                    return payment;
                })
                .collect(Collectors.toList()));

        return output;
    }

    public ResponseEntity<?> sendDDInfoMessages(
            Contract contract,
            boolean spouseWillSignDD,
            boolean ignoreErrorMsgs) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        if (spouseWillSignDD) {
            if (contract.getClient().getNormalizedSpouseMobileNumber() == null ||
                    contract.getClient().getNormalizedSpouseMobileNumber().isEmpty())
                throw new RuntimeException("There is no mobile number for spouse");

            if (cpt == null)
                throw new RuntimeException("There is no active payment term for this contract");

            cpt.setSpouseWillSignDD(true);
            contractPaymentTermRep.save(cpt);
        }

        // REFACTORED IN ACC-5078
        boolean existsIncomplete = directDebitRepository.existsIncompleteDDsByCpt(cpt);

        String errorMsg = null;
        if (!contract.getStatus().equals(ContractStatus.ACTIVE)
                && !contract.getStatus().equals(ContractStatus.PLANNED_RENEWAL)
                && !contract.getStatus().equals(ContractStatus.POSTPONED))

            errorMsg = "This feature is available for Active/Planned/POSTPONED Renewal contracts only";
        else if (cpt.isDdMsgsDisabled())
            errorMsg = "Direct Debit messaging is disabled for this contract";
        else if (!existsIncomplete &&
                directDebitRepository.existsDdNotCanceledAndNotDeletedByCpt(cpt))
            errorMsg = "There are no DDs to be signed";

        if(errorMsg != null) {
            if (ignoreErrorMsgs) return ResponseEntity.ok("Done");

            throw new RuntimeException(errorMsg);
        }
        // acc-1542 -> removed code

        Client client = cpt.getContract().getClient();
        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", cpt);
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "DD_PENDING_INFO");
        }});
        String signLink = utils.getSingDDLink(signDDMap);

        HashMap<String, Object> map = new HashMap<>();
        map.put("link_send_dd_details", signLink);
        map.put("templateOriginalName", "DD_PENDING_INFO");
        map.put("contract", contract);
        MessageTemplateService.messageTemplateSendNotification(map);

        if (cpt.getSpouseWillSignDD() && client.getNormalizedSpouseMobileNumber() != null) {
            Map<String, String> parameters = new HashMap<>();
            parameters.put("greetings", contract.isMaidCc() ?
                    Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC) :
                    Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
            parameters.put("link_send_dd_details", signLink);

            String templateName = contract.isMaidCc() ?
                    CcSmsTemplateCode.CC_DD_PENDING_INFO_SMS.toString() :
                    MvSmsTemplateCode.MV_DD_PENDING_INFO_SMS.toString();
            messagingService.sendClientSms(contract,
                            TemplateUtil.getTemplate(templateName),
                            parameters,
                            new HashMap<>(),
                            client.getNormalizedSpouseMobileNumber(),
                            client.getNormalizedWhatsappNumber(),
                            client.getId(),
                            client.getEntityType());
        }

        return ResponseEntity.ok("Done");
    }

    private void closeResolverToDos(Contract contract) {
        SelectQuery<VoiceResolverToDo> query = new SelectQuery(VoiceResolverToDo.class);
        query.filterBy("contract", "=", contract);
        query.filterBy("reason", "in", Arrays.asList(
                VoiceResolverToDoReason.PAYMENT_EXPIRY_INDEFINITE_AGREEMENT,
                VoiceResolverToDoReason.PAYMENT_EXPIRY_OLD_CC));
        query.filterBy("completed", "=", false);
        query.filterBy("stopped", "=", false);

        query.execute().forEach(t -> moduleConnector.get("clientmgmt/voiceResolverToDo/closetodo/" + t.getId(), Map.class));
    }

    public ResponseEntity<?> signDDByClientForExistingClient(
            Long contractId,
            String eid, String ibanNumber, String accountName,
            Attachment ibanPhoto, Attachment eidPhoto, Attachment accountPhoto,
            Map<String, Object> m) throws Exception {

        logger.log(Level.INFO, "SIGN DD BY CLIENT for existing client");
        Contract contract = contractRep.findOne(contractId);
        Client currentClient = contract.getClient();
        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

        Map<String, Object> lastSignatureType = directDebitSignatureService.getLastSignatureType(
                currentClient, eid, true, false);
        List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) lastSignatureType.get("currentSignatures");

        contractPaymentTermHelper.extractBankInfoWithoutOCR(contractPaymentTerm,
                eidPhoto, ibanPhoto, accountPhoto,
                eid, ibanNumber, accountName);

        if (contract.getStatus().equals(ContractStatus.POSTPONED)) {
            signDdPostponedContract(contractPaymentTerm,
                    directDebitSignatureService.getSignatureAttachmentsOnly(signatures),
                    false);
            logger.log(Level.SEVERE, "Finished SIGN DD BY CLIENT for existing client -> postponed contract");

            return new ResponseEntity<>(HttpStatus.OK);
        }

        logger.log(Level.INFO, "SIGN DD BY CLIENT entering sign dd by client");

        // ACC-8662
        // Check and Start Prevent Create Other Dds
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .validateAndStartPreventCreateOtherDds(contractPaymentTerm);

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);

        // Add waived payments before start generate DDs ACC-7050
        if (contract.isMaidVisa() && contract.getWaivedMonths() > 0) {
            paymentService.addWaivedPayment(contract);
        }

        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);

        contractService.updatePaidEndDate(contract);


        Map termResult = getContractPaymentTermByContractWithDirectDebitPayments(contractPaymentTerm);

        // SAVE PAYMENTS AND GENERATE DDS
        List<ContractPayment> payments = (List<ContractPayment>) termResult.get("payments");
        updateContractPaymentTermWithPayments(
                contractPaymentTerm, true, false, payments,
                signatures == null ? new ArrayList<>() :
                        directDebitSignatureService.getSignatureAttachmentsOnly(signatures),
                true, signatures == null, true,
                false, false, true, true, m);

        contractPaymentTermHelper.closeRelatedNotificationsForDDSinging(contractPaymentTerm.getContract());
        contractPaymentTermHelper.closeRelatedDDMessages(contractPaymentTerm.getContract());

        logger.log(Level.INFO, "Finished SIGN DD BY CLIENT for existing client");

        return new ResponseEntity("signing DD is being processed", HttpStatus.OK);
    }

    public PaymentTermConfig findSuitableConfig(
            PicklistItem nationality, PicklistItem contractProspectType,
            PaymentTermConfigType type, ContractPackageType packageType) {

        PaymentTermConfig paymentTermConfig = null;
        String url = null;
        try {
            url = "/sales/paymenttermconfig/findsuitableconfig/" +
                    nationality.getId() + "/" + contractProspectType.getId() + "/" +
                    type + (packageType != null ? ("?packageType=" + packageType) : "");

            logger.log(Level.SEVERE, "URL: " + url);
            LinkedHashMap responseEntity = connector.get(url, LinkedHashMap.class);

            Long paymentTermConfigId = Long.parseLong(responseEntity.get("id").toString());

            SelectQuery<PaymentTermConfig> query = new SelectQuery(PaymentTermConfig.class);
            query.leftJoinFetch("paymentTermsTemplate");
            query.leftJoinFetch("taxInvoiceTemplate");
            query.leftJoinFetch("paymentTypeConfigs");
            query.setLimit(1);

            query.filterBy("id", "=", paymentTermConfigId);

            paymentTermConfig = query.execute().get(0);

            entityManager.detach(paymentTermConfig);

            logger.log(Level.SEVERE, "Success: " + responseEntity.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

//        // Check if there is no PTC of Nationality
//        if (paymentTermConfig == null) {
//            throw new TechnicalException("There is no PTC specific to the nationality " + nationality.getName() +
//                        ", " + contractProspectType.getName() + ", " + type +
//                        (packageType != null ? (", " + packageType) : ""));
//        }
        return paymentTermConfig;
    }

    public PaymentTermConfigType getContractPaymentTypeOfContract(Contract contract, boolean newMaidLiveOut) {
        if (contract.isMaidCc()) {
            return newMaidLiveOut ? PaymentTermConfigType.LIVE_OUT : PaymentTermConfigType.LONG_TERM;
        }

        if (contract.isMaidVisa()) {
            return PaymentTermConfigType.valueOf(contract.getWorkerCurrentSituation().getCode().toUpperCase());
        }

        return PaymentTermConfigType.valueOf(contract.getContractType().name());
    }

    public Attachment generatePaymentsReceipt(
            Contract contract,
            WordTemplate wordTemplate,
            boolean replaceCurrent,
            boolean ignoreSignature) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        if (replaceCurrent) {
            Attachment attachment = cpt.getAttachment(FILE_TAG_PAYMENTS_RECEIPT);
            if (attachment != null) {
                attachementRepository.delete(attachment);
            }
        }

        if (wordTemplate == null) wordTemplate = cpt.getPaymentTermsTemplate();

        InputStream signatureForReceipt = getSignatureForPaymentReceipt(cpt);

        if (signatureForReceipt == null && !ignoreSignature) {
            throw new RuntimeException("couldn't regenerate Payment Receipt because there is no Signature");
        }

        try {
            Attachment a = PaymentReceiptHelper.generatePaymentsReceipt(
                    cpt, contract.getClient(), null, signatureForReceipt, wordTemplate);
            if (replaceCurrent && a != null) {
                cpt.addAttachment(a);
                contractPaymentTermRep.save(cpt);
            }

            return a;
        } catch (Exception ex) {
            logger.severe(ExceptionUtils.getStackTrace(ex));
            throw new RuntimeException(ex);
        } finally {
            StreamsUtil.closeStream(signatureForReceipt);
        }
    }

    public InputStream getSignatureForPaymentReceipt(ContractPaymentTerm cpt) {
        cpt = contractPaymentTermRep.findOne(cpt.getId());
        Contract contract = cpt.getContract();

        Attachment contractSignature = contract.getAttachments().stream()
                .filter(att -> att.getTag().equals(ContractController.SIGNATURE_FILE_TAG_NAME)).findFirst()
                .orElse(null);
        InputStream signatureForReceipt = null;
        // new block for payment details email signature
        if (contractSignature == null) {
            Map<String, Object> result = directDebitSignatureService
                    .getLastSignatureType(contract.getActiveContractPaymentTerm(), true, false);
            if (result.get("currentSignatures") != null) {
                contractSignature = ((List<DirectDebitSignature>) result.get("currentSignatures")).get(0)
                        .getSignatureAttachment();
                signatureForReceipt = Storage.getStream(contractSignature);
            }
        } else
            signatureForReceipt = Storage.getStream(contractSignature);

        return signatureForReceipt;
    }

    public Map switchNationality_amendingDirectDebitForms_getTerms(Contract contract,
                                                                   Housemaid currentHousemaid,
                                                                   Housemaid newHousemaid) throws Exception {
        Map response = new HashMap();
        Map<String, String> paymentTerms = new LinkedHashMap();
        String relatedCMSCode = "";
        Long templateObjectId = null;
        String templateObjectType = null;

        ContractPaymentTerm currentCPT = contract.getActiveContractPaymentTerm();
        Housemaid oldHousemaid = currentCPT.getHousemaid();

        Date upgradingNationalityDate = contract.getUpgradingNationalityDate();
        DateTime replacementDate;

        boolean hasNotCompletedReplacement = false;
        if (upgradingNationalityDate != null) {
            replacementDate = new DateTime(upgradingNationalityDate);
            hasNotCompletedReplacement = true;
        } else {
            replacementDate = DateTime.now();
        }

        boolean isFirstReplacement = upgradingNationalityDate == null && contract.getDowngradingNationalityDate() == null;

        boolean isReplacingCurrentMaidWithSameGradeNewMaid = currentHousemaid != null && !currentHousemaid.getId().equals(oldHousemaid.getId()) &&
                switchingNationalityService.getSwitchingNationalityType(currentHousemaid,
                        newHousemaid).equals(
                                SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE);

        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType =
                switchingNationalityService.getSwitchingNationalityType(oldHousemaid, newHousemaid);

        boolean isSwitchingNationality = !switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE) &&
                !isReplacingCurrentMaidWithSameGradeNewMaid;

        if (isSwitchingNationality) {
            if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.UPGRADING)) {

                Map<String, Object> m = switchNationalityNormalProratedPayments(
                        contract, oldHousemaid, newHousemaid, isFirstReplacement,
                        currentCPT, replacementDate, hasNotCompletedReplacement, false);
                relatedCMSCode = (String) m.get("amendDdCmsCode");
                paymentTerms = (Map<String, String>) m.get("paymentTerms");

            } else {
                if (isFirstReplacement) {
                    relatedCMSCode = "@DD_amendment_to_non_filipina@";
                } else {
                    relatedCMSCode = "@DD_second_amendment_to_non_filipina@";
                }
                templateObjectType = "Housemaid";
                templateObjectId = newHousemaid.getId();
            }
        }

        // params for CMSes @DD_amendment_to_non_filipina@ @DD_second_amendment_to_non_filipina@
        Map<String, Object> params = new HashMap<>();
        params.put("selected_nationality_price", Setup.getApplicationContext().getBean(CcAppContentHelper.class)
                .getMaidNationalityPrice(newHousemaid.getId(), currentCPT.getId()));

        response.put("switchingNationality", isSwitchingNationality);
        response.put("relatedCMS_CODE", relatedCMSCode);
        response.put("relatedCMS", !StringUtils.isEmpty(relatedCMSCode) ?
                Setup.getApplicationContext().getBean(TemplateUtil.class)
                        .compileTemplate(relatedCMSCode, templateObjectId, templateObjectType, params)
                : null);
        response.put("paymentTerms", DDUtils.getTermsAsString(paymentTerms));

        return response;
    }

    public Map<String, Object> switchNationality_amendingDirectDebitForms_getTermsCma3750(
            Contract contract, Housemaid currentHousemaid, Housemaid newHousemaid, DateTime replacementDate) throws Exception {

        Map<String, Object> response = new HashMap<>();

        ContractPaymentTerm currentCPT = contract.getActiveContractPaymentTerm();
        Housemaid oldHousemaid = currentCPT.getHousemaid();

        replacementDate = contract.getUpgradingNationalityDate() != null ?
                new DateTime(contract.getUpgradingNationalityDate()) : replacementDate;
        boolean hasNotCompletedReplacement = contract.getUpgradingNationalityDate() != null;
        boolean isFirstReplacement = contract.getUpgradingNationalityDate() == null && contract.getDowngradingNationalityDate() == null;

        // old nationality from contract with new nationality
        boolean isReplacingCurrentMaidWithSameGradeNewMaid = currentHousemaid != null && !currentHousemaid.getId().equals(oldHousemaid.getId()) &&
                switchingNationalityService.getSwitchingNationalityType(currentHousemaid, newHousemaid)
                        .equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE);

        // old nationality from cpt with new nationality
        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType =
                switchingNationalityService.getSwitchingNationalityType(oldHousemaid, newHousemaid);

        boolean isSwitchingNationality = !switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE) &&
                !isReplacingCurrentMaidWithSameGradeNewMaid;

        if (!isSwitchingNationality) {
            response.put("switchingNationality", false);
            response.put("relatedCMS_CODE", "");
            response.put("relatedCMS", null);
            response.put("paymentTerms", "");
            return response;
        }

        Map<String, Object> m = new HashMap<>();
        switch (switchingNationalityType) {
            case UPGRADING:
                m = handleUpgradingCase(contract, currentCPT, newHousemaid, oldHousemaid, isFirstReplacement, replacementDate, hasNotCompletedReplacement);
                break;
            case DOWNGRADING:
                m = handleDowngradingCase(contract, currentCPT, newHousemaid, isFirstReplacement, replacementDate);
                break;
        }

        boolean switchSameNationality = newHousemaid.getNationality().getCode().equalsIgnoreCase(oldHousemaid.getNationality().getCode());
        Map<String, String> params = new HashMap<>();
        params.put("new_nationality", StringUtils.getHousemaidNationalityOrLiveStatus(newHousemaid, switchSameNationality));
        params.put("old_nationality", StringUtils.getHousemaidNationalityOrLiveStatus(oldHousemaid, switchSameNationality));
        params.putAll((Map<String, String>) m.get("params"));
        response.put("paymentTerms", m.get("paymentTerms") instanceof String ?
                m.get("paymentTerms") : DDUtils.getTermsAsString((Map<String, String>) m.get("paymentTerms")));
        response.put("switchingNationality", m.containsKey("amendDdCmsCode") || m.containsKey("payingViaCcCmsCode"));
        response.put("amendDdNoteLiveOut", m.getOrDefault("amendDdNoteLiveOut", ""));

        response.put("amendDdCmsCode", m.get("amendDdCmsCode"));
        if (m.containsKey("amendDdCmsCode")) {
            response.put("amendDdCms", TemplateUtil.compileTemplate(
                    (String) m.get("amendDdCmsCode"), (String) m.get("templateObjectId"), m.get("templateObjectType"), params));
        }
        response.put("amendDd", m.containsKey("amendDdCmsCode"));

        response.put("payingViaCcCmsCode", m.get("payingViaCcCmsCode"));
        response.put("clientPayingViaCreditCard", m.containsKey("payments"));
        if (m.containsKey("payingViaCcCmsCode")) {
            response.put("payingViaCcCms", Setup.getApplicationContext().getBean(TemplateUtil.class)
                    .compileTemplate((String) m.get("payingViaCcCmsCode"),
                            new HashMap<>(),
                            params));

            if (m.containsKey("payments")) {
                List<HashMap<String, Object>> l = (List<HashMap<String, Object>>) m.get("payments");
                if (!l.isEmpty()) {
                    response.put("totalAmount", l.stream()
                            .mapToDouble(p -> (Double)p.get("amount"))
                            .sum());
                    response.put("paymentType", l.get(0).get("paymentType"));
                    response.put("payments", l);
                }
            }
        }

        return response;
    }

    private Map<String, Object> handleUpgradingCase(
            Contract contract, ContractPaymentTerm currentCPT, Housemaid newHousemaid, Housemaid oldHousemaid,
            boolean isFirstReplacement, DateTime replacementDate, boolean hasNotCompletedReplacement) throws Exception {

        Map<String, Object> r = new HashMap<>();
        Map<String, Object> params = new HashMap<>();

        if (flowProcessorService.isPayingViaCreditCard(contract) ||
                (contract.isOneMonthAgreement() &&
                        oneMonthAgreementFlowService.isPayingViaCreditCard(contract))) {

            Map<String, Object> m = oneMonthAgreementFlowService.isPayingViaCreditCard(contract) ?
                    oneMonthAgreementFlowService.getUpgradeNationalityPaymentInfoCreditCard(currentCPT, newHousemaid, false, replacementDate) :
                    clientPayingViaCreditCardService.getUpgradeNationalityPaymentInfoCreditCard(currentCPT, newHousemaid, replacementDate);

            r.put("payments", m.get("payments"));
            if (m.containsKey("parameters")) {
                params.putAll((Map<String, String>) m.get("parameters"));
                r.put("payingViaCcCmsCode", CcAppCmsTemplate.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SCREEN.toString());
            }

        } else if (contract.isOneMonthAgreement() && oneMonthAgreementFlowService.isPayingViaDirectDebit(contract)) {

            r.put("amendDdCmsCode", CcAppCmsTemplate.DD_AMENDMENT_ONE_MONTH_AGREEMENT_UPGRADE.toString());
            r.put("paymentTerms",  TemplateUtil.compileTemplate(
                    String.valueOf(CcAppCmsTemplate.DD_AMENDMENT_TO_FILIPINA_PAYMENT_TERMS),
                    contract,
                    oneMonthAgreementFlowService.getAmendDdPaymentTerms(currentCPT, newHousemaid, replacementDate)));

            r.put("amendDdNoteLiveOut", "");
            if (newHousemaid.getLiveOut()) {
                Map<String, Object> newAmendDdNoteLiveOut = new HashMap<>();
                newAmendDdNoteLiveOut.put("amend_dd_note_live_out", DDUtils.getSwitchMaidTermsAmendDDNoteLiveOutNewDesign(newHousemaid.getLiveOut()));
                r.put("amendDdNoteLiveOut",  TemplateUtil.compileTemplate(
                        String.valueOf(CcAppCmsTemplate.DD_AMENDMENT_NOTE_LIVE_OUT),
                        contract,
                        newAmendDdNoteLiveOut));
            }

            Map<String, Object> m = oneMonthAgreementFlowService.getUpgradeNationalityPaymentInfoCreditCard(
                    currentCPT, newHousemaid, true, replacementDate);
            r.put("payments", m.get("payments"));
            if (m.containsKey("parameters")) {
                params.putAll((Map<String, String>) m.get("parameters"));
                r.put("payingViaCcCmsCode", CcAppCmsTemplate.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SCREEN.toString());
            }
        } else {
            r.putAll(switchNationalityNormalProratedPayments(
                    contract, oldHousemaid, newHousemaid, isFirstReplacement,
                    currentCPT, replacementDate, hasNotCompletedReplacement, true));
        }

        r.put("params", params);
        return r;
    }

    private Map<String, Object> handleDowngradingCase(
            Contract contract, ContractPaymentTerm currentCPT,
            Housemaid newHousemaid, boolean isFirstReplacement, DateTime replacementDate) throws ClassNotFoundException {

        Map<String, Object> r = new HashMap<>();
        Map<String, Object> params = new HashMap<>();

        if (flowProcessorService.isPayingViaCreditCard(contract) ||
                (contract.isOneMonthAgreement() &&
                        oneMonthAgreementFlowService.isPayingViaCreditCard(contract))) {
            Map<String, Object> m = oneMonthAgreementFlowService.isPayingViaCreditCard(contract) ?
                    oneMonthAgreementFlowService.getDowngradeNationalityPaymentInfoCreditCard(currentCPT, newHousemaid, false, replacementDate) :
                    clientPayingViaCreditCardService.getDowngradeNationalityPaymentInfoCreditCard(currentCPT, newHousemaid, replacementDate);
            r.put("payments", m.get("payments"));
            if (m.containsKey("parameters")) {
                params.putAll((Map<String, String>) m.get("parameters"));
                r.put("payingViaCcCmsCode", CcAppCmsTemplate.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SCREEN.toString());
            }
        } else if (contract.isOneMonthAgreement() && oneMonthAgreementFlowService.isPayingViaDirectDebit(contract)) {
            params.putAll(oneMonthAgreementFlowService.getDowngradeNationalityPaymentInfoDirectDebit(currentCPT, newHousemaid, replacementDate));
            r.put("amendDdCmsCode", CcAppCmsTemplate.DD_AMENDMENT_ONE_MONTH_AGREEMENT_DOWNGRADE.toString());

            Map<String, Object> m = oneMonthAgreementFlowService.getDowngradeNationalityPaymentInfoCreditCard(
                    currentCPT, newHousemaid, true, replacementDate);
            r.put("payments", m.get("payments"));
            if (m.containsKey("parameters")) { // need to check with ccapp team
                params.putAll((Map<String, String>) m.get("parameters"));
                r.put("payingViaCcCmsCode", CcAppCmsTemplate.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SCREEN.toString());
            }

        } else {
            r.put("amendDdCmsCode", isFirstReplacement ? "@DD_amendment_to_non_filipina@" : "@DD_second_amendment_to_non_filipina@");
            r.put("templateObjectType", "Housemaid");
            r.put("templateObjectId", String.valueOf(newHousemaid.getId()));
            params.put("selected_nationality_price", Setup.getApplicationContext().getBean(CcAppContentHelper.class)
                    .getMaidNationalityPrice(newHousemaid.getId(), currentCPT.getId()));

        }

        r.put("params", params);
        return r;
    }

    public int getDowngradeNationalityRefundAmount(
            Contract contract, ContractPaymentTerm currentCPT,
            Housemaid newHousemaid, DateTime replacementDate) throws Exception {

        ContractPaymentTerm newCpt = switchingNationalityService.simulateNewCPTCreation(
                currentCPT.getContract(), currentCPT, newHousemaid.getNationality(), newHousemaid.getLiveOut(), currentCPT.getContract().getClientPaidVat(),
                SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING,replacementDate);
        Double amount = 0.0;
        if (contract.isPayingViaCreditCard() ||
                (contract.isOneMonthAgreement() &&
                        oneMonthAgreementFlowService.isPayingViaCreditCard(contract))) {
            amount = contract.isPayingViaCreditCard() ?
                    clientPayingViaCreditCardService.getRefundAmount(currentCPT, newCpt, replacementDate) :
                    oneMonthAgreementFlowService.getCreditCardRefundAmount(currentCPT, newCpt, replacementDate);
        } else if (contract.isOneMonthAgreement() && oneMonthAgreementFlowService.isPayingViaDirectDebit(contract)) {

            amount = oneMonthAgreementFlowService.getDirectDebitRefundAmount(
                    currentCPT, newCpt, replacementDate);
        }

        return amount.intValue();
    }

    public Map<String, Object> switchNationalityNormalProratedPayments(
        Contract contract, Housemaid oldHousemaid, Housemaid newHousemaid, boolean isFirstReplacement,
        ContractPaymentTerm currentCPT, DateTime replacementDate, boolean hasNotCompletedReplacement, boolean withNewDesign) throws Exception {

        boolean clientPaidVat = contract.getClientPaidVat() != null && contract.getClientPaidVat();

        ContractPaymentTerm newCPT = switchingNationalityService.simulateNewCPTCreation(
                contract, currentCPT, newHousemaid.getNationality(), newHousemaid.getLiveOut(), clientPaidVat,
                SwitchingNationalityService.SwitchingNationalityType.UPGRADING, replacementDate);

        Double thisMonthCurrentAmount = switchingNationalityService.getCPTAmountAtTime(replacementDate.toDate(), currentCPT, false);
        Double thisMonthNewAmount = switchingNationalityService.getCPTAmountAtTime(replacementDate.toDate(), newCPT, false);

        Integer replacementMonthDays = replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth();
        Double proratedRate = (thisMonthNewAmount - thisMonthCurrentAmount) / replacementMonthDays;

        Double nextMonthCurrentAmount = switchingNationalityService.getCPTAmountAtTime(replacementDate.plusMonths(1).toDate(), currentCPT, false);
        Double nextMonthNewAmount = switchingNationalityService.getCPTAmountAtTime(replacementDate.plusMonths(1).toDate(), newCPT, false);

        boolean beforeFirstOfNextMonth = replacementDate.plusDays(Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SWITCHING_NATIONALITY_TRIAL_DAYS)))
                .withTimeAtStartOfDay().isBefore(replacementDate.plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay());

        Date date = beforeFirstOfNextMonth ?
                replacementDate.plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate() :
                replacementDate.plusDays(15).withTimeAtStartOfDay().toDate();

        if (clientPaidVat) {
            proratedRate = DiscountsWithVatHelper.getAmountWithoutVat(proratedRate);
            nextMonthNewAmount = DiscountsWithVatHelper.getAmountWithoutVat(nextMonthNewAmount);
            nextMonthCurrentAmount = DiscountsWithVatHelper.getAmountWithoutVat(nextMonthCurrentAmount);
        }

        String amendDdCmsCode;
        if (beforeFirstOfNextMonth) {
            if (isFirstReplacement) {
                amendDdCmsCode = "@DD_amendment_to_filipina_before_first@";
            } else {
                amendDdCmsCode = "@DD_second_amendment_to_filipina_before_first@";
            }
        } else {
            if (isFirstReplacement) {
                amendDdCmsCode = "@DD_amendment_to_filipina_after_first@";
            } else {
                amendDdCmsCode = "@DD_second_amendment_to_filipina_after_first@";
            }
        }

        Map pricingDifference = getSwitchMaidPricingDifference(contract.getId(), newHousemaid.getId(), replacementDate, true);

        if (withNewDesign){
            Map r = new HashMap<String, Object>();
            Map<String, Object> newPaymentTerms = new HashMap<>();
            newPaymentTerms.put("one_time_terms", DDUtils.getSwitchMaidTerms_OneTime_NewDesign(
                    replacementDate, proratedRate, nextMonthNewAmount, nextMonthCurrentAmount,
                    date, newHousemaid.getNationality().getName(), newHousemaid.getLiveOut()));

            newPaymentTerms.put("monthly_terms", DDUtils.getSwitchMaidTerms_Monthly_NewDesign(
                    pricingDifference, replacementDate, newHousemaid.getNationality().getName(), newHousemaid.getLiveOut()));

            r.put("amendDdCmsCode", amendDdCmsCode);
            r.put("paymentTerms", TemplateUtil.compileTemplate(
                    String.valueOf(CcAppCmsTemplate.DD_AMENDMENT_TO_FILIPINA_PAYMENT_TERMS),
                    contract,
                    newPaymentTerms));
            r.put("amendDdNoteLiveOut", "");

            if (newHousemaid.getLiveOut()) {
                Map<String, Object> newAmendDdNoteLiveOut = new HashMap<>();
                newAmendDdNoteLiveOut.put("amend_dd_note_live_out", DDUtils.getSwitchMaidTermsAmendDDNoteLiveOutNewDesign(newHousemaid.getLiveOut()));
                r.put("amendDdNoteLiveOut", TemplateUtil.compileTemplate(
                        String.valueOf(CcAppCmsTemplate.DD_AMENDMENT_NOTE_LIVE_OUT),
                        contract,
                        newAmendDdNoteLiveOut));
            }

            return r;
        } else {
            Map<String, String> paymentTerms = DDUtils.getSwitchMaidTerms_OneTime(replacementDate, proratedRate, nextMonthNewAmount, nextMonthCurrentAmount,
                    date,
                    newHousemaid.getNationality().getName(), oldHousemaid.getNationality().getName(),
                    hasNotCompletedReplacement);

            paymentTerms.putAll(DDUtils.getSwitchMaidTerms_Monthly(pricingDifference, replacementDate, newHousemaid.getNationality().getName()));
            return new HashMap<String, Object>() {{
                put("amendDdCmsCode", amendDdCmsCode);
                put("paymentTerms", paymentTerms);
                put("amendDdNoteLiveOut", "");
            }};
        }
    }

    public Map getSwitchMaidPricingDifference(Long contractId,
                                              Long newHousemaidId,
                                              DateTime replacementDate,
                                              boolean withoutOneTime) {

        if (contractId == null || newHousemaidId == null) {
            throw new RuntimeException("Contract ID & New Housemaid ID must not be NULL");
        }

        Map response = new LinkedHashMap();
        Contract contract = contractRep.findOne(contractId);
        Housemaid newHousemaid = Setup.getRepository(HousemaidRepository.class).findOne(newHousemaidId);
        switchingNationalityService.validateSwitchMaid(contract, newHousemaid);
        ContractPaymentTerm currentCPT = (ContractPaymentTerm) contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract)
                .get("contractPaymentTerm");

        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType = switchingNationalityService.getSwitchingNationalityType(
                currentCPT.getHousemaid(), newHousemaid);

        if (!switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE)) {
            boolean clientPaidVat = contract.getClientPaidVat() != null && contract.getClientPaidVat();

            ContractPaymentTerm newCPT = switchingNationalityService.simulateNewCPTCreation(
                    contract, currentCPT, newHousemaid.getNationality(), newHousemaid.getLiveOut(),
                    clientPaidVat, switchingNationalityType, replacementDate);

            List<DirectDebit> tempDDs = switchingNationalityService.getSwitchMaidNewDDs(currentCPT, newCPT, replacementDate);
            response = DDUtils.getPricingDifferenceForCCAPP_SwitchMaid(currentCPT, tempDDs, withoutOneTime, clientPaidVat);
        }

        return response;
    }

    @Transactional
    public void extractSignaturesByOCR(Long cptId, List<String> signaturesAsAttachmentsIds, Long firstSignatureOcrId,
                                       Map<String, Object> payload, String contractUUID, Long contractId,
                                       String eid, String iban, String account, Boolean eidPhotoChanged,
                                       Boolean ibanPhotoChanged, Boolean accountNamePhotoChanged,
                                       Boolean useOldSignature, Boolean needToSign,
                                       List<LinkedHashMap> cashPayments, Boolean pendingOcr) throws Exception {
        logger.log(Level.INFO, "contractPaymentTerm id : " + cptId);
        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        List<Attachment> signaturesAsAttachments = attachementRepository
                .findAll(signaturesAsAttachmentsIds
                            .stream()
                            .map(Long::parseLong)
                            .collect(Collectors.toList()));

        try {
            List<Attachment> signaturesAfterOcr = new ArrayList<>();
            signaturesAfterOcr.add(attachementRepository.findOne(firstSignatureOcrId));
            signaturesAsAttachments.forEach(signatureAsAttachment -> {
                logger.info("process signatureAsAttachment id : " + signatureAsAttachment.getId());

                // The attachment name contains the attachment ID of the original signature.
                Attachment signature = directDebitSignatureService.extractSignature(Storage.getStream(signatureAsAttachment),
                        "Temp" + signatureAsAttachment.getId() + " Signature.png" , "temp_signature");

                if (!validateExtractedSignatureFromOcrUsingGPT(signature)) {
                    throw new RuntimeException("signature is not valid from gpt");
                }


                if (signature != null) signaturesAfterOcr.add(signature);
            });

            // sign DD By Client
            ((List<Map<String, Object>>) payload.get("attachments"))
                    .addAll(signaturesAfterOcr
                            .stream()
                            .map(a -> new HashMap<String, Object>() {{ put("id", a.getId()); }})
                            .collect(Collectors.toList()));
            logger.info("payload map: " + payload);

            signDDByClientAsyncBGT(payload, contractUUID, eid, iban, account,
                    eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                    useOldSignature, needToSign,
                    cashPayments,
                    null, null, null,
                    pendingOcr);
        } catch (Exception e) {
            e.printStackTrace();

            Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);
            createGraphicDesignerToDoFromVisaAsync(contract, signaturesAsAttachments.stream()
                    .map(signature -> Collections.singletonMap("id", signature.getId()))
                    .collect(Collectors.toList()));
        }
    }

    public void createGraphicDesignerToDoFromVisaAsync(Contract contract, List<Map<String, Long>> signaturesAsAttachments) throws Exception {

        logger.info("ceateGraphicDesignerToDo for Contract: " + contract.getId());

        Map<String, Object> body = new HashMap<>();
        body.put("taskName", "Digitalize client signature");
        body.put("toDoType", "ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE");
        body.put("contractId", contract.getId());
        body.put("clientId", contract.getClient().getId());
        body.put("attachments", signaturesAsAttachments);

        moduleConnector.postJsonAsync("visa/GraphicDesignerWorkFlow/create", body);
    }

    // MC-28
    public boolean allowSigningDD(Contract contract) {
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        boolean clientHasPendingGraphicDesignerToDos = contract.isSigningPaperMode() &&
                Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class)
                        .doesClientHavePendingDesignerToDo(contract);

        boolean existsIncomplete = directDebitRepository.existsIncompleteDDsByCpt(cpt);
        boolean existsDDs = directDebitRepository.existsByContractPaymentTerm_Contract(contract);

        // ACC-7421
        boolean allDdCanceledPerCpt = existsDDs && directDebitRepository.allDdCanceledByCpt(cpt);
        return (existsIncomplete || !existsDDs || allDdCanceledPerCpt) && !clientHasPendingGraphicDesignerToDos &&
                !ContractScheduleForTerminationUtils.isTerminationReasonDueClientPayingViaCreditCardFlows(contract);
    }

    public boolean validateExtractedSignatureFromOcrUsingGPT(Attachment attachment) {
        try {
            if (attachment == null) return false;
            Template template = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase("gpt_validate_extracted_signature");
            if (template == null) throw new BusinessException("Template with name: gpt_validate_extracted_signature should be defined!!");

            String result = chatAIService.sendToChatGPT(new ChatAIRequestBuilder()
                    .template(template)
                    .templateParam(new HashMap<String, String>() {{
                        put("img","img");
                    }})
                    .attachments(new HashMap<String, Attachment>() {{
                        put("img", attachment);
                    }}));
            logger.info("result from gpt: " + result + " attachment id: " + attachment.getId());
            return result.equalsIgnoreCase("YES");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Transactional
    public void deleteCreditCardToken(ContractPaymentTerm cpt) { deleteCreditCardToken(cpt, true); }

    @Transactional
    public void deleteCreditCardToken(ContractPaymentTerm cpt, boolean sendTokenDeletedMessage) {

        logger.info("cpt id: " + cpt.getId());

        if(cpt.getSourceId() == null) return;

        contractPaymentTermRep.save(this.removeTokenFromCpt(cpt));

        paymentService.removeRecurringPayment(cpt);

        if(!sendTokenDeletedMessage) return;

        int days = Integer.parseInt(Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card)
                .getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue()) + 2;

        if (Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .existsRunningFlow(cpt.getContract(),
                        FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                        Collections.singletonList(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER)) ||
                Setup.getRepository(FlowProcessorEntityRepository.class).existsStoppedMonthlyReminder(cpt.getContract(),
                            new LocalDate().minusDays(days).toDate())) {
            logger.info("Monthly reminder is running");

            Setup.getApplicationContext()
                    .getBean(ClientPayingViaCreditCardService.class)
                    .sendNewMessageForTokenDeletionSubEvent(cpt);
        }
    }

    public ContractPaymentTerm copyTokenToNewCpt(ContractPaymentTerm oldCpt, ContractPaymentTerm newCpt) {
        return updateTokenOnCpt(oldCpt.getSourceId(), oldCpt.getSourceAmount(), oldCpt.getSourceInfoAsString(), oldCpt.getProvider(), newCpt);
    }

    public ContractPaymentTerm updateTokenOnCpt(String sourceId, Double sourceAmount, String sourceInfoAsString, EPaymentProvider ePaymentProvider, ContractPaymentTerm newCpt) {
        newCpt.setSourceId(sourceId);
        newCpt.setSourceAmount(sourceAmount);
        newCpt.setSourceInfo(sourceInfoAsString);
        newCpt.setProvider(ePaymentProvider);
        return newCpt;
    }

    public ContractPaymentTerm removeTokenFromCpt(ContractPaymentTerm cpt) {
        cpt.setSourceId(null);
        cpt.setSourceAmount(null);
        cpt.setSourceInfo(null);
        cpt.setProvider(null);
        return cpt;
    }

    public void disableAllCcOfferLinkOfCptByBGT(ContractPaymentTerm cpt){

        logger.info("cpt id: " + cpt.getId());

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "disableAllCcOfferLinkOfCPT_" + cpt.getId(),
                        "accounting",
                        "contractPaymentTermServiceNew",
                        "disableAllCcOfferLinkOfCPT")
                        .withRelatedEntity("ContractPaymentTerm", cpt.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {cpt.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public void disableAllCcOfferLinkOfCPT(Long cptId) {
        logger.info("cpt id: " + cptId);
        ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository =
                Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
        ContractPaymentTerm cpt = contractPaymentTermRep.findOne(cptId);
        // disable all active-links of cpt
        List<ContractPaymentConfirmationToDo> l = contractPaymentConfirmationToDoRepository
                .findAllConfirmationToDoByContractPaymentTermAndCreditCardOffer(cpt);
        if (l.isEmpty()) return;

        for (ContractPaymentConfirmationToDo todo : l) {
            todo.setDisabled(true);

            // disable all credit_card_offer_cta of Push Notifications by ConfirmationToDo
            Setup.getRepository(DisablePushNotificationRepository.class)
                    .findActiveNotificationsByOwner(todo.getRelatedEntityId(), todo.getRelatedEntityType())
                    .forEach(p -> messagingService.disablePushNotificationCtaByName(p, "credit_card_offer_cta"));
        }

        contractPaymentConfirmationToDoRepository.saveAll(l);
    }
}