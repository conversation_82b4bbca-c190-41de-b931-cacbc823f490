# PLVariableBucket API Fix

## Issue Description

The `/accounting/plvariablebuckets/create` endpoint was throwing a 500 Internal Server Error with the message:
```
"Variable should not be empty."
```

## Root Cause

The issue was in the validation logic of the `BasePLVariableBucket` entity. The `@BeforeInsert` validation method was checking if the `pLVariable` field was null, but the default `BaseRepositoryController.createEntity` method was not setting this relationship before validation.

### Code Flow:
1. POST request to `/accounting/plvariablebuckets/create`
2. `BaseRepositoryController.createEntity()` called (from magnamedia-core)
3. Entity saved directly, triggering `@BeforeInsert` validation
4. `BasePLVariableBucket.validate()` checks `this.getpLVariable() == null`
5. Throws `RuntimeException("Variable should not be empty.")`

## Solution

Added custom `createEntity` methods to both:
- `PLVariableBucketController`
- `AdhocVariableBucketController`

These methods:
1. Validate required fields (weight, range)
2. Check that PLVariable is provided in the request
3. Fetch the full PLVariable entity from database if only ID is provided
4. Set the relationship before calling `super.createEntity()`

## API Usage

### Correct Request Format

When creating a PLVariableBucket, include the PLVariable reference in your JSON:

```json
{
  "wieght": 0.5,
  "pLVariable": {
    "id": 123
  },
  "expense": {
    "id": 456
  }
}
```

OR

```json
{
  "wieght": -0.3,
  "pLVariable": {
    "id": 123
  },
  "revenue": {
    "id": 789
  }
}
```

### Required Fields:
- `wieght`: Double between -1 and +1
- `pLVariable`: Object with valid `id` of existing PLVariableNode
- Either `expense` OR `revenue` (not both)

### Validation Rules:
- Weight must be between -1 and +1 (inclusive)
- PLVariable must exist in database
- PLVariable cannot be null

## Files Modified

1. `src/main/java/com/magnamedia/controller/PLVariableBucketController.java`
   - Added `createEntity` method override
   - Added validation and relationship setup

2. `src/main/java/com/magnamedia/controller/AdhocVariableBucketController.java`
   - Added `createEntity` method override
   - Added validation and relationship setup

## Testing

To test the fix:

1. **Valid Request:**
```bash
POST /accounting/plvariablebuckets/create
Content-Type: application/json

{
  "wieght": 0.5,
  "pLVariable": {"id": 1},
  "expense": {"id": 1}
}
```

2. **Invalid Requests (should return proper error messages):**
```bash
# Missing weight
{"pLVariable": {"id": 1}, "expense": {"id": 1}}

# Invalid weight range
{"wieght": 2.0, "pLVariable": {"id": 1}, "expense": {"id": 1}}

# Missing PLVariable
{"wieght": 0.5, "expense": {"id": 1}}

# Invalid PLVariable ID
{"wieght": 0.5, "pLVariable": {"id": 999999}, "expense": {"id": 1}}
```

## Error Messages

The fix provides clear error messages:
- `"Bucket weight should not be empty."`
- `"Bucket weight should be between -1 and +1."`
- `"Variable should not be empty. Please provide the PLVariable in the request."`
- `"PLVariable with id {id} not found."`

## Notes

- The fix maintains backward compatibility
- Both PLVariableBucket and AdhocVariableBucket endpoints are fixed
- The validation logic is consistent with the original `BasePLVariableBucket.validate()` method
- The fix follows the same pattern used in other controllers in the codebase
